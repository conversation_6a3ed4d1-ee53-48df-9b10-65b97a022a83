import React, { useContext, useState } from 'react';
import PropTypes from 'prop-types';
import { Row, Col, notification } from 'antd';
import {
  Container,
  Label,
  Value,
  ButtonContainer,
  StyledButton,
} from './Styles/StyledSubmit';
import request from '../../utils/request';
import { API_ENDPOINTS } from '../../containers/EmployeeProfile/constants';
import { EmployeeProfileContext } from '../../containers/EmployeeProfile/context';

const Submit = ({ ids, calculatedPLIScore, disabled }) => {
  const { pliDataMap, profile, selectedMonth, onPLISubmitted } =
    useContext(EmployeeProfileContext) || {};
  const [submitting, setSubmitting] = useState(false);

  const formatDataForSubmission = () => {
    if (!pliDataMap || !profile) {
      return null;
    }

    // Convert month name to number
    const months = {
      january: 1,
      february: 2,
      march: 3,
      april: 4,
      may: 5,
      june: 6,
      july: 7,
      august: 8,
      september: 9,
      october: 10,
      november: 11,
      december: 12,
    };
    const month =
      selectedMonth && months[selectedMonth.toLowerCase()]
        ? months[selectedMonth.toLowerCase()]
        : new Date().getMonth() + 1;
    const year = new Date().getFullYear();

    // Format the data for submission
    const projectRatings = [];

    Object.keys(pliDataMap).forEach(projectKey => {
      const [projectId, projectTypeRaw] = projectKey.split('_');

      const projectType = projectTypeRaw || 'Fixed'; // Default to Fixed if not provided
      const projectData = pliDataMap[projectKey];

      // Create parameter scores array
      const parameterScores = [];

      // Group parameters by their parent parameter
      const allParams = new Set();
      const allSprints = new Set();

      // Collect all parameters and sprints
      Object.keys(projectData).forEach(sprintName => {
        if (
          sprintName !== 'parameterId' &&
          sprintName !== 'comments' &&
          sprintName !== 'projectWeightage'
        ) {
          allSprints.add(sprintName);
          Object.keys(projectData[sprintName]).forEach(paramName => {
            allParams.add(paramName);
          });
        }
      });

      // Get parameterId from ids or generate a placeholder for Super Admin
      let parameterId;
      if (ids.parameterIds && ids.parameterIds[projectKey]) {
        parameterId = ids.parameterIds[projectKey];
      } else if (
        ids.isSuperAdminUser &&
        ids.parameterMappings &&
        ids.parameterMappings[projectId]
      ) {
        // For Super Admin, use the parameterId from the parameterMappings
        parameterId = ids.parameterMappings[projectId];
      } else {
        // For regular users, we need a valid parameterId
        parameterId = null;
      }

      // Create a parameter score object
      const parameterScore = {
        parameterId,
        parentParameter: 'Project',
        projectType,
        comments: projectData.comments || '',
        childScores: [],
      };

      // Add child scores for each parameter
      allParams.forEach(paramName => {
        const childScore = {
          childParameterId: paramName,
          sprintScores: [],
        };

        // Add sprint scores for this parameter
        allSprints.forEach(sprintName => {
          if (projectData[sprintName] && projectData[sprintName][paramName]) {
            // Directly access the comment string from the sprint score data
            const comment = projectData[sprintName][paramName].comment || '';

            const sprintScore = {
              sprintNumber: sprintName,
              score: projectData[sprintName][paramName].value || 0, // Default to 0 if value is missing
              comment, // Include the comment string
            };
            childScore.sprintScores.push(sprintScore);
          }
        });

        parameterScore.childScores.push(childScore);
      });

      parameterScores.push(parameterScore);

      // Add project rating
      const projectRating = {
        projectId,
        projectWeightage: parseInt(projectData.projectWeightage || 100, 10),
        parameterScores,
      };

      projectRatings.push(projectRating);
    });

    // Ensure menteeId is included, even for Super Admin override
    const menteeId = ids.menteeId || profile.menteeId || profile.empId;
    if (!menteeId) {
      notification.error({
        message: 'Error',
        description: 'Mentee ID is required',
      });
      return null;
    }

    const formattedData = {
      menteeId,
      mentorId: ids.mentorId,
      month,
      year,
      projectRatings,
      pliRatingId: ids.pliRatingId, // Include pliRatingId if it exists
      superAdminOverride: ids.isSuperAdminUser === true, // Ensure this is a boolean true
    };

    return formattedData;
  };

  const handleSubmit = async () => {
    try {
      setSubmitting(true);
      const formattedData = formatDataForSubmission();

      if (!formattedData) {
        notification.error({
          message: 'Error',
          description: 'No data available to submit',
        });
        setSubmitting(false);
        return;
      }

      // Always use the standard endpoint, but include superAdminOverride in the body
      const response = await request(API_ENDPOINTS.UPDATE_SPRINT_SCORES, {
        method: 'POST',
        body: JSON.stringify(formattedData),
        headers: {
          'Content-Type': 'application/json',
          'X-Super-Admin-Override': formattedData.superAdminOverride
            ? 'true'
            : 'false', // Add custom header
        },
      });

      if (response && response.status === 1) {
        notification.success({
          message: 'Success',
          description: formattedData.superAdminOverride
            ? 'PLI data overridden successfully by Super Admin'
            : 'Sprint scores submitted successfully',
        });
        if (onPLISubmitted) {
          onPLISubmitted();
        }
      } else {
        throw new Error(
          response && response.message
            ? response.message
            : 'Failed to submit sprint scores',
        );
      }
    } catch (error) {
      notification.error({
        message: 'Error',
        description: error.message || 'Failed to submit sprint scores',
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Container>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Label>Calculated PLI Score</Label>
          <Value>{calculatedPLIScore || 'N/A'}</Value>
        </Col>

        <Col span={24}>
          <ButtonContainer>
            <StyledButton
              type="primary"
              loading={submitting}
              onClick={handleSubmit}
              disabled={disabled}
            >
              Submit
            </StyledButton>
          </ButtonContainer>
        </Col>
      </Row>
    </Container>
  );
};

Submit.propTypes = {
  ids: PropTypes.shape({
    menteeId: PropTypes.string,
    mentorId: PropTypes.string,
    parameterIds: PropTypes.object,
    pliRatingId: PropTypes.string,
    isSuperAdminUser: PropTypes.bool,
  }),
  calculatedPLIScore: PropTypes.number,
  disabled: PropTypes.bool,
};

Submit.defaultProps = {
  ids: null,
  calculatedPLIScore: null,
  disabled: false,
};

export default Submit;
