import React from 'react';
import PropTypes from 'prop-types';
import { Row, Col } from 'antd';
import {
  DetailCard,
  Label,
  Value,
  DetailRow,
  HeaderCell,
  TextAreaContainer,
  StyledTextArea,
  EditIcon,
  SaveButton,
} from './StyledPLIDetails';

const PLIDetails = ({
  profile = {},
  pliData,
  sprintHeaders,
  comments,
  isEditing,
  setComments,
  toggleEditing,
  isEditable = true,
}) => {
  if (!pliData || sprintHeaders.length === 0) {
    return null;
  }

  const paramSpan = 4;
  const calcSpan = 3;
  const weightSpan = 3;
  const sprintCount = sprintHeaders.length;
  const sprintSpan = 2;
  const totalSpan =
    paramSpan + calcSpan + weightSpan + sprintSpan * sprintCount;

  const isScrollable = totalSpan > 24;

  return (
    <Row>
      <Col>
        <div style={isScrollable ? { overflowX: 'auto' } : {}}>
          <DetailCard title={`${profile.projectName} - ${profile.projectType}`}>
            <DetailRow>
              <Col span={paramSpan}>
                <Label>Parameters</Label>
              </Col>
              {sprintHeaders.map(s => (
                <Col key={s} span={2} style={{ minWidth: 60, padding: '4px' }}>
                  <HeaderCell style={{ fontSize: '12px', padding: '4px' }}>
                    {s}
                  </HeaderCell>
                </Col>
              ))}
              <Col span={calcSpan}>
                <HeaderCell>Calculation</HeaderCell>
              </Col>
              <Col span={weightSpan}>
                <HeaderCell>Weightage Avg</HeaderCell>
              </Col>
            </DetailRow>

            {Object.keys(pliData[sprintHeaders[0]] || {}).map(param => (
              <DetailRow key={param}>
                <Col span={paramSpan}>
                  <Label>{param}</Label>
                </Col>
                {sprintHeaders.map(s => (
                  <Col
                    key={`${s}-${param}`}
                    span={2}
                    style={{ minWidth: 60, padding: '4px' }}
                  >
                    <Value style={{ fontSize: '12px', padding: '4px' }}>
                      {pliData[s] &&
                      pliData[s][param] &&
                      pliData[s][param].value !== undefined
                        ? pliData[s][param].value
                        : 'N/A'}
                    </Value>
                  </Col>
                ))}
                <Col span={calcSpan}>
                  <Value>
                    {pliData[sprintHeaders[0]] &&
                    pliData[sprintHeaders[0]][param] &&
                    pliData[sprintHeaders[0]][param].calculation !== undefined
                      ? pliData[sprintHeaders[0]][param].calculation
                      : 'N/A'}
                  </Value>
                </Col>
                <Col span={weightSpan}>
                  <Value>
                    {(() =>
                      pliData[sprintHeaders[0]] &&
                      pliData[sprintHeaders[0]][param] &&
                      (pliData[sprintHeaders[0]][param].weightageAverage !==
                        undefined ||
                        pliData[sprintHeaders[0]][param].weightage !==
                          undefined)
                        ? pliData[sprintHeaders[0]][param].weightageAverage ||
                          pliData[sprintHeaders[0]][param].weightage
                        : 'N/A')()}
                  </Value>
                </Col>
              </DetailRow>
            ))}

            <TextAreaContainer>
              <Label>Comments</Label>
              {isEditable && <EditIcon onClick={toggleEditing} />}
              <StyledTextArea
                value={comments}
                onChange={e => setComments(e.target.value)}
                disabled={!isEditable || !isEditing}
                placeholder={
                  isEditable
                    ? 'Enter your comments here...'
                    : 'Comments are read-only in this view'
                }
              />
            </TextAreaContainer>

            {isEditable && <SaveButton type="primary">Save</SaveButton>}
          </DetailCard>
        </div>
      </Col>
    </Row>
  );
};

PLIDetails.propTypes = {
  profile: PropTypes.shape({
    projectName: PropTypes.string,
    projectType: PropTypes.string,
  }),
  pliData: PropTypes.object,
  sprintHeaders: PropTypes.arrayOf(PropTypes.string),
  comments: PropTypes.string,
  isEditing: PropTypes.bool,
  setComments: PropTypes.func,
  toggleEditing: PropTypes.func,
  isEditable: PropTypes.bool,
};

export default PLIDetails;
