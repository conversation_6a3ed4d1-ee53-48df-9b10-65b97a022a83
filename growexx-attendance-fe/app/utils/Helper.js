/* eslint-disable no-return-assign */
/* eslint-disable no-unused-vars */
import { TOKEN_KEY, USER_DATA_KEY } from './constants';
import StorageService from './StorageService';

export function userExists() {
  if (StorageService.exists(TOKEN_KEY)) {
    return true;
  }
  return false;
}

/**
 * Get User Data
 * @returns
 */
export function getUserData() {
  if (StorageService.exists(TOKEN_KEY)) {
    return StorageService.get(USER_DATA_KEY);
  }
  return false;
}

/**
 * to logout user from system
 */
export function logout() {
  StorageService.clear();
}

// eslint-disable-next-line consistent-return
export function getNumbersOnly({ e, min, max, precision }) {
  const regex = /^[0-9]+([.][0-9])?$/;
  let { value } = e.target;
  const previousValues = value.substring(0, value.length - 1);

  // Allow empty string to clear the field
  if (value === '') {
    return 0;
  }

  if (value.includes('.')) {
    // eslint-disable-next-line eqeqeq
    if (precision == 0) return (e.target.value = previousValues);
    const splitNum = value.split('.'); /* means values has multiple dots */
    if (splitNum.length > 2) return (e.target.value = previousValues);
    if (!regex.test(splitNum[0]) || (splitNum[1] && !regex.test(splitNum[1])))
      return (e.target.value = previousValues);
    if (splitNum[1].length > precision)
      return (e.target.value = previousValues);
  } else if (!regex.test(value)) return (e.target.value = previousValues);

  value = parseFloat(value);
  if (min && value < min) return (e.target.value = previousValues);
  if (max && value > max) return (e.target.value = previousValues);
  // eslint-disable-next-line eqeqeq
  if (value == 0 && value.length > 2) return (e.target.value = 0);
}
