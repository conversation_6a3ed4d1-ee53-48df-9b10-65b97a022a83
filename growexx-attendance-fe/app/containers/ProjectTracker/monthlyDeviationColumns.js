import { monthNames } from '../constants';

// Helper function to format numbers to 2 decimal places
const formatNumber = value => {
  if (value !== undefined && value !== null) {
    return Number(value).toFixed(2);
  }
  return '';
};
export const monthlyDeviationColumns = () => [
  {
    headerName: 'Project Name',
    field: 'projectName',
    width: 160,
    pinned: 'left',
  },

  {
    headerName: 'Month/Year',
    valueGetter: params => {
      const monthIndex = params.data.month - 1;
      const { year } = params.data;
      const monthYear = `${monthNames[monthIndex]}/${year}`;
      return `${monthYear} (CurrentMonthData-PreviousMonthData)`;
    },
    width: 180,
    pinned: 'left',
  },
  {
    headerName: 'Monthly Data',
    children: [
      {
        headerName: 'Total Hours',
        children: [
          {
            headerName: 'Planned',
            valueGetter: params => {
              const plannedValue = params.data.currentData.totalHours.planned;
              const plannedDeviation =
                params.data.deviations.totalHours.planned;
              return `${plannedValue} (${plannedDeviation.value} (${
                plannedDeviation.percentage
              }%)`;
            },
            type: 'rightAligned',
            width: 190,
          },
          {
            headerName: 'Actual',
            valueGetter: params => {
              const actualValue = params.data.currentData.totalHours.actual;
              const actualDeviation = params.data.deviations.totalHours.actual;
              return `${actualValue} (${actualDeviation.value} (${
                actualDeviation.percentage
              }%)`;
            },
            type: 'rightAligned',
            width: 190,
          },
        ],
      },
      {
        headerName: 'Epic Level Data',
        children: [
          {
            headerName: 'Done',
            children: [
              {
                headerName: 'Sales Estimate',
                valueGetter: params => {
                  const salesEstimateValue =
                    params.data.currentData.metrics.done.salesEstimate;
                  const salesEstimateDeviation =
                    params.data.deviations.metrics.done.salesEstimate;
                  return `${salesEstimateValue} (${
                    salesEstimateDeviation.value
                  } (${salesEstimateDeviation.percentage}%)`;
                },
                type: 'rightAligned',
                width: 190,
              },
              {
                headerName: 'Spent',
                valueGetter: params => {
                  const spentValue = params.data.currentData.metrics.done.spent;
                  const spentDeviation =
                    params.data.deviations.metrics.done.spent;
                  return `${spentValue} (${spentDeviation.value} (${
                    spentDeviation.percentage
                  }%)`;
                },
                type: 'rightAligned',
                width: 190,
              },
              {
                headerName: 'To Be Spent',
                valueGetter: params => {
                  const toBeSpentValue =
                    params.data.currentData.metrics.done.toBeSpent;
                  const toBeSpentDeviation =
                    params.data.deviations.metrics.done.toBeSpent;
                  return `${formatNumber(toBeSpentValue)} (${formatNumber(
                    toBeSpentDeviation.value,
                  )} (${formatNumber(toBeSpentDeviation.percentage)}%)`;
                },
                type: 'rightAligned',
                width: 190,
              },
              {
                headerName: 'Difference',
                valueGetter: params => {
                  const differenceValue =
                    params.data.currentData.metrics.done.difference;
                  const differenceDeviation =
                    params.data.deviations.metrics.done.difference;
                  return `${differenceValue} (${differenceDeviation.value} (${
                    differenceDeviation.percentage
                  }%)`;
                },
                type: 'rightAligned',
                width: 190,
                cellStyle: params => ({
                  backgroundColor: params.value < 0 ? '#ffcdd2' : '#c8e6c9',
                }),
              },
            ],
          },
          {
            headerName: 'In Progress',
            children: [
              {
                headerName: 'Sales Estimate',
                valueGetter: params => {
                  const salesEstimateValue =
                    params.data.currentData.metrics.inProgress.salesEstimate;
                  const salesEstimateDeviation =
                    params.data.deviations.metrics.inProgress.salesEstimate;
                  return `${salesEstimateValue} (${
                    salesEstimateDeviation.value
                  } (${salesEstimateDeviation.percentage}%)`;
                },
                type: 'rightAligned',
                width: 190,
              },
              {
                headerName: 'Spent',
                valueGetter: params => {
                  const spentValue =
                    params.data.currentData.metrics.inProgress.spent;
                  const spentDeviation =
                    params.data.deviations.metrics.inProgress.spent;
                  return `${spentValue} (${spentDeviation.value} (${
                    spentDeviation.percentage
                  }%)`;
                },
                type: 'rightAligned',
                width: 190,
              },
              {
                headerName: 'To Be Spent',
                valueGetter: params => {
                  const toBeSpentValue =
                    params.data.currentData.metrics.inProgress.toBeSpent;
                  const toBeSpentDeviation =
                    params.data.deviations.metrics.inProgress.toBeSpent;
                  return `${formatNumber(toBeSpentValue)} (${formatNumber(
                    toBeSpentDeviation.value,
                  )} (${formatNumber(toBeSpentDeviation.percentage)}%)`;
                },
                type: 'rightAligned',
                width: 190,
              },
              {
                headerName: 'Difference',
                valueGetter: params => {
                  const differenceValue =
                    params.data.currentData.metrics.inProgress.difference;
                  const differenceDeviation =
                    params.data.deviations.metrics.inProgress.difference;
                  return `${differenceValue} (${differenceDeviation.value} (${
                    differenceDeviation.percentage
                  }%)`;
                },
                type: 'rightAligned',
                width: 190,
                cellStyle: params => ({
                  backgroundColor: params.value < 0 ? '#ffcdd2' : '#c8e6c9',
                }),
              },
            ],
          },
          {
            headerName: 'To Do',
            children: [
              {
                headerName: 'Sales Estimate',
                valueGetter: params => {
                  const salesEstimateValue =
                    params.data.currentData.metrics.toDo.salesEstimate;
                  const salesEstimateDeviation =
                    params.data.deviations.metrics.toDo.salesEstimate;
                  return `${salesEstimateValue} (${
                    salesEstimateDeviation.value
                  } (${salesEstimateDeviation.percentage}%)`;
                },
                type: 'rightAligned',
                width: 190,
              },
              {
                headerName: 'Spent',
                valueGetter: params => {
                  const spentValue = params.data.currentData.metrics.toDo.spent;
                  const spentDeviation =
                    params.data.deviations.metrics.toDo.spent;
                  return `${spentValue} (${spentDeviation.value} (${
                    spentDeviation.percentage
                  }%)`;
                },
                type: 'rightAligned',
                width: 190,
              },
              {
                headerName: 'To Be Spent',
                valueGetter: params => {
                  const toBeSpentValue =
                    params.data.currentData.metrics.toDo.toBeSpent;
                  const toBeSpentDeviation =
                    params.data.deviations.metrics.toDo.toBeSpent;
                  return `${formatNumber(toBeSpentValue)} (${formatNumber(
                    toBeSpentDeviation.value,
                  )} (${formatNumber(toBeSpentDeviation.percentage)}%)`;
                },
                type: 'rightAligned',
                width: 190,
              },
              {
                headerName: 'Difference',
                valueGetter: params => {
                  const differenceValue =
                    params.data.currentData.metrics.toDo.difference;
                  const differenceDeviation =
                    params.data.deviations.metrics.toDo.difference;
                  return `${differenceValue} (${differenceDeviation.value} (${
                    differenceDeviation.percentage
                  }%)`;
                },
                type: 'rightAligned',
                width: 190,
                cellStyle: params => ({
                  backgroundColor: params.value < 0 ? '#ffcdd2' : '#c8e6c9',
                }),
              },
            ],
          },
        ],
      },
    ],
  },
  {
    headerName: 'Main Difference',
    valueGetter: params => {
      const { value } = params.data.currentData.difference;
      const { percentage } = params.data.currentData.difference;
      return `${value} (${percentage}%)`;
    },
    type: 'rightAligned',
    width: 140,
    cellStyle: params => ({
      backgroundColor:
        params.data.currentData.difference.value < 0 ||
        params.data.currentData.difference.percentage < 0
          ? '#ffcdd2'
          : '#c8e6c9',
    }),
  },
];
