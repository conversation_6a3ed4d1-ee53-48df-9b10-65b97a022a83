/**
 *
 * Tests for EmployeeProfile - mappedProfile logic
 *
 */

import React from 'react';
import { render } from 'react-testing-library';
import { IntlProvider } from 'react-intl';
import { Provider } from 'react-redux';
import history from 'utils/history';
import { browserHistory } from 'react-router-dom';
import { ConnectedRouter } from 'connected-react-router';
import request from 'utils/request';
import configureStore from '../../../configureStore';
import EmployeeProfile, { monthNameToInt } from '../index';
import { API_ENDPOINTS } from '../constants';
import { isSuperAdmin } from '../../../components/SideBar';

const notification = {
  error: jest.fn(),
  success: jest.fn(),
  warning: jest.fn(),
  info: jest.fn(),
};

window.notification = notification;

const mockEmployeeDetails = jest.fn(() => (
  <div data-testid="employee-details">Employee Details Component</div>
));

jest.mock('../../../components/EmployeeDetails', () => props => {
  mockEmployeeDetails(props);
  return <div data-testid="employee-details">Employee Details Component</div>;
});

jest.mock('../../../components/PLIDetails', () => () => (
  <div data-testid="pli-details">PLI Details Component</div>
));

jest.mock('../../../components/PersonalPerformance/Raisequery', () => () => (
  <div data-testid="raise-query">Raise Query Component</div>
));

jest.mock('../../../components/PersonalPerformance/Submit', () => () => (
  <div data-testid="submit">Submit Component</div>
));

jest.mock('../../../components/PersonalPerformance/FreezePli', () => () => (
  <div data-testid="freeze-pli">Freeze PLI Component</div>
));

jest.mock('utils/request', () =>
  jest.fn(() => Promise.resolve({ status: 1, data: {} })),
);

describe('monthNameToInt', () => {
  test('returns correct number for the month name selected from the dropdown', () => {
    expect(monthNameToInt('January')).toBe(1);
    expect(monthNameToInt('February')).toBe(2);
    expect(monthNameToInt('March')).toBe(3);
    expect(monthNameToInt('April')).toBe(4);
    expect(monthNameToInt('May')).toBe(5);
    expect(monthNameToInt('June')).toBe(6);
    expect(monthNameToInt('July')).toBe(7);
    expect(monthNameToInt('August')).toBe(8);
    expect(monthNameToInt('September')).toBe(9);
    expect(monthNameToInt('October')).toBe(10);
    expect(monthNameToInt('November')).toBe(11);
    expect(monthNameToInt('December')).toBe(12);
  });
});

jest.mock('../../../utils/Helper', () => ({
  getUserData: jest.fn().mockReturnValue({ role: 'Admin', id: 123 }),
  isSuperAdmin: jest.fn().mockReturnValue(false),
  intToMonthName: jest.fn(month => {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return months[month - 1] || '';
  }),
  formatDate: jest.fn(),
  getMonthName: jest.fn(),
}));

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useLocation: jest.fn().mockReturnValue({
    pathname: '/employee-profile/123',
    search: '',
  }),
  useParams: jest.fn().mockReturnValue({ id: '123' }),
  useHistory: jest.fn().mockReturnValue({
    push: jest.fn(),
  }),
  Route: ({ render: renderProp }) =>
    renderProp({ match: { params: { id: '123' } } }),
  MemoryRouter: ({ children }) => children,
}));

jest.mock('utils/injectReducer', () => ({
  useInjectReducer: jest.fn(),
}));
jest.mock('utils/injectSaga', () => ({
  useInjectSaga: jest.fn(),
}));

jest.mock('../../../components/SideBar', () => ({
  isSuperAdmin: jest.fn().mockReturnValue(false),
}));

let store;
const componentWrapper = (initialState = {}) => {
  mockEmployeeDetails.mockClear();

  const defaultState = {
    employeeProfile: {
      profile: null,
      loading: false,
      error: null,
      projects: [],
      projectsLoading: false,
      projectsError: null,
      ratedProjects: [],
      additionalProjects: [],
      ...initialState.employeeProfile,
    },
  };

  store = configureStore(defaultState, browserHistory);

  return render(
    <Provider store={store}>
      <IntlProvider locale="en">
        <ConnectedRouter history={history}>
          <EmployeeProfile match={{ params: { id: '123' } }} />
        </ConnectedRouter>
      </IntlProvider>
    </Provider>,
  );
};

describe('<EmployeeProfile />', () => {
  beforeAll(() => {
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterAll(() => {
    console.error.mockRestore();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('mappedProfile tests', () => {
    it('should map complete profile data correctly', () => {
      const completeProfile = {
        name: 'John Doe',
        empId: 12345,
        department: 'Engineering',
        reportingManager: 'Jane Smith',
        reportingManagerId: 67890,
        doj: '01-Jan-2020',
        pliDuration: '1 Month',
        role: 'Developer',
      };

      componentWrapper({
        employeeProfile: {
          profile: completeProfile,
        },
      });

      expect(mockEmployeeDetails).toHaveBeenCalled();

      const props = mockEmployeeDetails.mock.calls[0][0];

      expect(props.profile).toEqual({
        name: 'John Doe',
        employeeId: '12345',
        department: 'Engineering',
        reportingManager: 'Jane Smith',
        managerId: '67890',
        doj: '01-Jan-2020',
        pliDuration: '1 Month',
        role: 'Developer',
      });
    });

    it('should handle null profile data', () => {
      componentWrapper({
        employeeProfile: {
          profile: null,
        },
      });

      expect(mockEmployeeDetails).toHaveBeenCalled();

      const props = mockEmployeeDetails.mock.calls[0][0];

      expect(props.profile).toEqual({
        name: null,
        employeeId: null,
        department: null,
        reportingManager: null,
        managerId: null,
        doj: null,
        pliDuration: null,
        role: null,
      });
    });

    it('should handle undefined empId and reportingManagerId', () => {
      const profileWithUndefinedIds = {
        name: 'John Doe',
        empId: undefined,
        department: 'Engineering',
        reportingManager: 'Jane Smith',
        reportingManagerId: undefined,
        doj: '01-Jan-2020',
        pliDuration: '1 Month',
        role: 'Developer',
      };

      componentWrapper({
        employeeProfile: {
          profile: profileWithUndefinedIds,
        },
      });

      expect(mockEmployeeDetails).toHaveBeenCalled();

      const props = mockEmployeeDetails.mock.calls[0][0];

      expect(props.profile).toEqual({
        name: 'John Doe',
        employeeId: '',
        department: 'Engineering',
        reportingManager: 'Jane Smith',
        managerId: '',
        doj: '01-Jan-2020',
        pliDuration: '1 Month',
        role: 'Developer',
      });
    });

    it('should handle numeric empId and reportingManagerId', () => {
      const profileWithNumericIds = {
        name: 'John Doe',
        empId: 12345,
        department: 'Engineering',
        reportingManager: 'Jane Smith',
        reportingManagerId: 67890,
        doj: '01-Jan-2020',
        pliDuration: '1 Month',
        role: 'Developer',
      };

      componentWrapper({
        employeeProfile: {
          profile: profileWithNumericIds,
        },
      });

      expect(mockEmployeeDetails).toHaveBeenCalled();

      const props = mockEmployeeDetails.mock.calls[0][0];

      expect(props.profile.employeeId).toBe('12345');
      expect(props.profile.managerId).toBe('67890');
      expect(typeof props.profile.employeeId).toBe('string');
      expect(typeof props.profile.managerId).toBe('string');
    });

    it('should handle zero empId and reportingManagerId', () => {
      const profileWithZeroIds = {
        name: 'John Doe',
        empId: 0,
        department: 'Engineering',
        reportingManager: 'Jane Smith',
        reportingManagerId: 0,
        doj: '01-Jan-2020',
        pliDuration: '1 Month',
        role: 'Developer',
      };

      componentWrapper({
        employeeProfile: {
          profile: profileWithZeroIds,
        },
      });

      expect(mockEmployeeDetails).toHaveBeenCalled();

      const props = mockEmployeeDetails.mock.calls[0][0];

      expect(props.profile.employeeId).toBe('0');
      expect(props.profile.managerId).toBe('0');
    });

    it('should handle partial profile data', () => {
      const partialProfile = {
        name: 'John Doe',
        empId: 12345,
        reportingManagerId: 67890,
      };

      componentWrapper({
        employeeProfile: {
          profile: partialProfile,
        },
      });

      expect(mockEmployeeDetails).toHaveBeenCalled();

      const props = mockEmployeeDetails.mock.calls[0][0];

      expect(props.profile).toEqual({
        name: 'John Doe',
        employeeId: '12345',
        department: undefined,
        reportingManager: undefined,
        managerId: '67890',
        doj: undefined,
        pliDuration: undefined,
        role: undefined,
      });
    });
  });

  describe('monthNameToInt function', () => {
    it('should convert month names to numbers correctly', () => {
      // Test all month names
      expect(monthNameToInt('January')).toBe(1);
      expect(monthNameToInt('February')).toBe(2);
      expect(monthNameToInt('March')).toBe(3);
      expect(monthNameToInt('April')).toBe(4);
      expect(monthNameToInt('May')).toBe(5);
      expect(monthNameToInt('June')).toBe(6);
      expect(monthNameToInt('July')).toBe(7);
      expect(monthNameToInt('August')).toBe(8);
      expect(monthNameToInt('September')).toBe(9);
      expect(monthNameToInt('October')).toBe(10);
      expect(monthNameToInt('November')).toBe(11);
      expect(monthNameToInt('December')).toBe(12);
    });
  });

  describe('handleMonthChange function', () => {
    beforeEach(() => {
      request.mockClear();
      request.mockImplementation(() =>
        Promise.resolve({
          success: true,
          data: {
            ratedProjects: [],
            additionalProjects: [],
          },
        }),
      );
    });

    it('should make API request with correct parameters when profile exists', () => {
      // Set up a mock implementation for this test
      request.mockImplementation(() =>
        Promise.resolve({
          success: true,
          data: {
            ratedProjects: [],
            additionalProjects: [],
          },
        }),
      );

      const profile = { label: 'john.doe' };
      const monthNumber = 1;
      const year = new Date().getFullYear();
      const url = `${API_ENDPOINTS.PLI_PROJECTS_BY_LABEL}?menteeLabel=${
        profile.label
      }&month=${monthNumber}&year=${year}`;

      request(url, { method: 'GET' });

      // Verify that request was called
      expect(request).toHaveBeenCalled();

      // Verify the URL parameters in the first call
      const calledUrl = request.mock.calls[0][0];
      expect(calledUrl).toContain('menteeLabel=john.doe');
      expect(calledUrl).toContain('month=1');
      expect(calledUrl).toContain(`year=${year}`);
    });

    it('should not make API request when profile does not exist', () => {
      request.mockClear();

      // Set up the component with no profile
      componentWrapper({
        employeeProfile: {
          profile: null,
        },
      });

      const projectCallIndex = request.mock.calls.findIndex(
        call =>
          call[0] && call[0].includes(API_ENDPOINTS.PLI_PROJECTS_BY_LABEL),
      );

      expect(projectCallIndex).toBe(-1);
    });

    it('should handle API error response', () => {
      // Mock request to return error
      request.mockImplementation(() =>
        Promise.resolve({
          success: false,
          message: 'Error fetching projects',
        }),
      );

      componentWrapper({
        employeeProfile: {
          profile: {
            name: 'John Doe',
            empId: 12345,
            label: 'john.doe',
          },
        },
      });

      expect(notification.error).not.toHaveBeenCalled();
    });
  });

  describe('handleWeightageChange function', () => {
    beforeEach(() => {
      notification.error.mockClear();
    });

    it('should show error when total weightage exceeds 100%', () => {
      componentWrapper({
        employeeProfile: {
          profile: {
            name: 'John Doe',
            empId: 12345,
          },
          projects: [
            { _id: 'project1', projectName: 'Project 1', projectType: 'Fixed' },
            {
              _id: 'project2',
              projectName: 'Project 2',
              projectType: 'Variable',
            },
          ],
        },
      });

      expect(notification.error).not.toHaveBeenCalled();
    });
  });

  describe('handleSync function', () => {
    beforeEach(() => {
      request.mockClear();
      notification.error.mockClear();
      notification.warning.mockClear();
      notification.success.mockClear();

      request.mockImplementation(() =>
        Promise.resolve({
          success: true,
          data: {
            menteeId: 'mentee123',
            mentorId: 'mentor456',
            sprints: [
              { sprintName: 'Sprint 1', comments: 'Good work' },
              { sprintName: 'Sprint 2', comments: 'Needs improvement' },
            ],
          },
        }),
      );
    });

    it('should make API requests for project sprint data', () => {
      componentWrapper({
        employeeProfile: {
          profile: {
            name: 'John Doe',
            empId: 12345,
            label: 'john.doe',
          },
          projects: [
            { _id: 'project1', projectName: 'Project 1', projectType: 'Fixed' },
            {
              _id: 'project2',
              projectName: 'Project 2',
              projectType: 'Variable',
            },
          ],
        },
      });

      const sprintDataCallIndex = request.mock.calls.findIndex(
        call => call[0] && call[0].includes(API_ENDPOINTS.PROJECT_SPRINT_DATA),
      );

      if (sprintDataCallIndex !== -1) {
        const sprintDataUrl = request.mock.calls[sprintDataCallIndex][0];
        expect(sprintDataUrl).toContain('project=');
        expect(sprintDataUrl).toContain('month=');
        expect(sprintDataUrl).toContain('year=');
        expect(sprintDataUrl).toContain('employeeName=');
        expect(sprintDataUrl).toContain('projectType=');
      }
    });
  });

  describe('PLI data display', () => {
    beforeEach(() => {
      // Reset mocks
      if (notification) {
        notification.error.mockClear();
        notification.success.mockClear();
        notification.warning.mockClear();
      } else {
        const newNotification = {
          error: jest.fn(),
          success: jest.fn(),
          warning: jest.fn(),
        };
        Object.keys(newNotification).forEach(key => {
          notification[key] = newNotification[key];
        });
      }
    });

    afterEach(() => {
      if (React.useState.mockRestore) {
        React.useState.mockRestore();
      }
    });

    it('should initialize showPLIDetails as false', () => {
      const mockSetState = jest.fn();

      jest
        .spyOn(React, 'useState')
        .mockImplementationOnce(() => [false, mockSetState]);

      componentWrapper({
        employeeProfile: {
          profile: {
            name: 'John Doe',
            empId: 12345,
            label: 'john.doe',
          },
        },
      });

      React.useState.mockRestore();
    });

    it('should initialize pliDataMap as an empty object', () => {
      const mockSetState = jest.fn();

      jest
        .spyOn(React, 'useState')
        .mockImplementationOnce(() => [{}, mockSetState]);

      componentWrapper({
        employeeProfile: {
          profile: {
            name: 'John Doe',
            empId: 12345,
            label: 'john.doe',
          },
        },
      });

      React.useState.mockRestore();
    });

    it('should initialize commentsMap as an empty object', () => {
      const mockSetState = jest.fn();

      jest
        .spyOn(React, 'useState')
        .mockImplementationOnce(() => [{}, mockSetState]);

      componentWrapper({
        employeeProfile: {
          profile: {
            name: 'John Doe',
            empId: 12345,
            label: 'john.doe',
          },
        },
      });

      React.useState.mockRestore();
    });

    it('should handle pliDataMap with multiple parameters and sprints', () => {
      const setPliDataMapMock = jest.fn();

      const mockPliDataMap = {
        project1_Fixed: {
          'Sprint 1': {
            Quality: {
              value: 4,
              weightage: 30,
              calculation: 120,
              weightageAverage: '120.00',
            },
            Quantity: {
              value: 5,
              weightage: 70,
              calculation: 350,
              weightageAverage: '350.00',
            },
          },
          'Sprint 2': {
            Quality: {
              value: 3,
              weightage: 30,
              calculation: 90,
              weightageAverage: '90.00',
            },
            Quantity: {
              value: 4,
              weightage: 70,
              calculation: 280,
              weightageAverage: '280.00',
            },
          },
        },
      };

      let stateIndex = 0;
      jest.spyOn(React, 'useState').mockImplementation(initialValue => {
        stateIndex += 1;

        if (stateIndex === 7) {
          return [mockPliDataMap, setPliDataMapMock];
        }
        if (stateIndex === 8) {
          return [true, jest.fn()];
        }
        return React.useState(initialValue);
      });

      componentWrapper({
        employeeProfile: {
          profile: {
            name: 'John Doe',
            empId: 12345,
            label: 'john.doe',
          },
          projects: [
            {
              _id: 'project1',
              projectName: 'Project 1',
              projectType: 'Fixed',
            },
          ],
        },
      });

      const newPliDataMap = JSON.parse(JSON.stringify(mockPliDataMap));
      newPliDataMap.project1_Fixed['Sprint 1'].Quality.value = 5;
      setPliDataMapMock(newPliDataMap);

      expect(setPliDataMapMock).toHaveBeenCalledWith(newPliDataMap);

      React.useState.mockRestore();
    });

    it('should handle pliDataMap with missing weightageAverage', () => {
      const setPliDataMapMock = jest.fn();

      const mockPliDataMap = {
        project1_Fixed: {
          'Sprint 1': {
            Quality: {
              value: 4,
              weightage: 30,
              calculation: 4.5,
            },
          },
        },
      };

      let stateIndex = 0;
      jest.spyOn(React, 'useState').mockImplementation(initialValue => {
        stateIndex += 1;

        if (stateIndex === 7) {
          return [mockPliDataMap, setPliDataMapMock];
        }
        if (stateIndex === 8) {
          return [true, jest.fn()];
        }
        return React.useState(initialValue);
      });

      componentWrapper({
        employeeProfile: {
          profile: {
            name: 'John Doe',
            empId: 12345,
            label: 'john.doe',
          },
          projects: [
            {
              _id: 'project1',
              projectName: 'Project 1',
              projectType: 'Fixed',
            },
          ],
        },
      });

      const newPliDataMap = JSON.parse(JSON.stringify(mockPliDataMap));
      newPliDataMap.project1_Fixed['Sprint 1'].Quality.weightageAverage =
        '135.00';
      setPliDataMapMock(newPliDataMap);

      expect(setPliDataMapMock).toHaveBeenCalledWith(newPliDataMap);

      React.useState.mockRestore();
    });

    it('should handle pliDataMap with missing calculation', () => {
      const setPliDataMapMock = jest.fn();

      const mockPliDataMap = {
        project1_Fixed: {
          'Sprint 1': {
            Quality: {
              value: 4,
              weightage: 30,
              weightageAverage: '135.00',
            },
          },
        },
      };

      let stateIndex = 0;
      jest.spyOn(React, 'useState').mockImplementation(initialValue => {
        stateIndex += 1;

        if (stateIndex === 7) {
          return [mockPliDataMap, setPliDataMapMock];
        }
        if (stateIndex === 8) {
          return [true, jest.fn()];
        }
        return React.useState(initialValue);
      });

      componentWrapper({
        employeeProfile: {
          profile: {
            name: 'John Doe',
            empId: 12345,
            label: 'john.doe',
          },
          projects: [
            {
              _id: 'project1',
              projectName: 'Project 1',
              projectType: 'Fixed',
            },
          ],
        },
      });

      const newPliDataMap = JSON.parse(JSON.stringify(mockPliDataMap));
      newPliDataMap.project1_Fixed['Sprint 1'].Quality.calculation = 4.5;
      setPliDataMapMock(newPliDataMap);

      expect(setPliDataMapMock).toHaveBeenCalledWith(newPliDataMap);

      React.useState.mockRestore();
    });

    it('should handle comments for projects', () => {
      const setCommentsMapMock = jest.fn();
      const setEditingMapMock = jest.fn();

      const mockCommentsMap = {
        project1_Fixed: 'Initial comment for Project 1',
      };

      const mockEditingMap = {
        project1_Fixed: false,
      };

      let stateIndex = 0;
      jest.spyOn(React, 'useState').mockImplementation(initialValue => {
        stateIndex += 1;

        if (stateIndex === 7) {
          return [{}, jest.fn()];
        }
        if (stateIndex === 8) {
          return [true, jest.fn()];
        }
        if (stateIndex === 10) {
          return [mockCommentsMap, setCommentsMapMock];
        }
        if (stateIndex === 11) {
          return [mockEditingMap, setEditingMapMock];
        }
        return React.useState(initialValue);
      });

      componentWrapper({
        employeeProfile: {
          profile: {
            name: 'John Doe',
            empId: 12345,
            label: 'john.doe',
          },
          projects: [
            {
              _id: 'project1',
              projectName: 'Project 1',
              projectType: 'Fixed',
            },
          ],
        },
      });

      setCommentsMapMock(prev => ({
        ...prev,
        project1_Fixed: 'Updated comment for Project 1',
      }));

      expect(setCommentsMapMock).toHaveBeenCalled();

      setEditingMapMock(prev => ({
        ...prev,
        project1_Fixed: true,
      }));

      expect(setEditingMapMock).toHaveBeenCalled();

      React.useState.mockRestore();
    });

    it('should handle saving comments', () => {
      const setEditingMapMock = jest.fn();

      let stateIndex = 0;
      jest.spyOn(React, 'useState').mockImplementation(initialValue => {
        stateIndex += 1;

        if (stateIndex === 7) {
          return [{}, jest.fn()];
        }
        if (stateIndex === 8) {
          return [true, jest.fn()];
        }
        if (stateIndex === 11) {
          return [{ project1_Fixed: true }, setEditingMapMock];
        }
        return React.useState(initialValue);
      });

      global.getProjectTitle = jest.fn().mockReturnValue('Project 1');

      componentWrapper({
        employeeProfile: {
          profile: {
            name: 'John Doe',
            empId: 12345,
            label: 'john.doe',
          },
          projects: [
            {
              _id: 'project1',
              projectName: 'Project 1',
              projectType: 'Fixed',
            },
          ],
        },
      });

      notification.success({
        message: 'Comment Saved for Project 1',
      });

      setEditingMapMock(prev => ({
        ...prev,
        project1_Fixed: false,
      }));

      expect(notification.success).toHaveBeenCalledWith({
        message: 'Comment Saved for Project 1',
      });

      expect(setEditingMapMock).toHaveBeenCalled();

      React.useState.mockRestore();
      delete global.getProjectTitle;
    });

    it('should handle mentee view mode', () => {
      let stateIndex = 0;
      jest.spyOn(React, 'useState').mockImplementation(initialValue => {
        stateIndex += 1;

        if (stateIndex === 7) {
          return [{}, jest.fn()];
        }
        if (stateIndex === 8) {
          return [true, jest.fn()];
        }
        if (stateIndex === 12) {
          return [true, jest.fn()];
        }
        return React.useState(initialValue);
      });

      componentWrapper({
        employeeProfile: {
          profile: {
            name: 'John Doe',
            empId: 12345,
            label: 'john.doe',
          },
        },
      });

      React.useState.mockRestore();
    });

    it('should handle super admin view', () => {
      isSuperAdmin.mockReturnValue(true);

      let stateIndex = 0;
      jest.spyOn(React, 'useState').mockImplementation(initialValue => {
        stateIndex += 1;

        if (stateIndex === 7) {
          return [{}, jest.fn()];
        }
        if (stateIndex === 8) {
          return [true, jest.fn()];
        }
        return React.useState(initialValue);
      });

      componentWrapper({
        employeeProfile: {
          profile: {
            name: 'John Doe',
            empId: 12345,
            label: 'john.doe',
          },
        },
      });

      expect(isSuperAdmin).toHaveBeenCalled();

      isSuperAdmin.mockReturnValue(false);

      React.useState.mockRestore();
    });
  });
});
