import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { connect } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { PageHeader, notification, Row, Col, Input } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import PropTypes from 'prop-types';
import { createStructuredSelector } from 'reselect';
import { compose } from 'redux';
import { useInjectSaga } from 'utils/injectSaga';
import { useInjectReducer } from 'utils/injectReducer';
import { fetchEmployeeProfile, fetchEmployeeProjects } from './actions';
import { makeSelectEmployeeProfile } from './selectors';
import { isSuperAdmin } from '../../components/SideBar';
import { getUserData } from '../../utils/Helper';
import EmployeeDetails from '../../components/EmployeeDetails';
import RaiseQuery from '../../components/PersonalPerformance/Raisequery';
import Submit from '../../components/PersonalPerformance/Submit';
import {
  DetailCard,
  Label,
  DetailRow,
  HeaderCell,
  TextAreaContainer,
  StyledTextArea,
  SaveButton,
  StatusRibbon,
} from '../../components/PLIDetails/StyledPLIDetails';
import request from '../../utils/request';
import { API_ENDPOINTS } from './constants';
import saga from './saga';
import reducer from './reducer';
import FreezePLI from '../../components/PersonalPerformance/FreezePli';
import { EmployeeProfileProvider } from './context';
import ScoreEditModal from '../../components/PersonalPerformance/ScoreEditModal';

const key = 'employeeProfile';

export function monthNameToInt(monthName) {
  const months = {
    january: 1,
    february: 2,
    march: 3,
    april: 4,
    may: 5,
    june: 6,
    july: 7,
    august: 8,
    september: 9,
    october: 10,
    november: 11,
    december: 12,
  };
  if (!monthName) return null;
  return months[monthName.toLowerCase()] || null;
}

function intToMonthName(monthNum) {
  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];
  return months[monthNum - 1] || 'January';
}

const EmployeeProfile = ({ fetchProfile, match, profileState }) => {
  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga });

  const [selectedMonth, setSelectedMonth] = useState(null);
  const [projectRows, setProjectRows] = useState([
    {
      id: 1,
      project: null,
      projectType: null,
      weightage: '',
      projectName: null,
    },
  ]);
  const [loading, setLoading] = useState(false);
  const [showPLIDetails, setShowPLIDetails] = useState(false);
  const [pliDataMap, setPliDataMap] = useState({});
  const [commentsMap, setCommentsMap] = useState({});
  const [editingMap, setEditingMap] = useState({});
  const [, setProjects] = useState([]);
  const [pliExists, setPliExists] = useState(false);
  const [isFrozen, setIsFrozen] = useState(false);
  const [pliRatingId, setPliRatingId] = useState(null);
  const [, setPliRating] = useState(null);
  const [isSuperAdminOverridden, setIsSuperAdminOverridden] = useState(false);
  const [scoresModified, setScoresModified] = useState(false);
  const [ids, setIds] = useState({
    menteeId: null,
    mentorId: null,
    isSuperAdminUser: null,
    parameterIds: {},
  });

  // State for the ScoreEditModal
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalData, setModalData] = useState(null);

  const { profile, ratedProjects, additionalProjects } = profileState || {};

  // mentee view page
  const location = useLocation();
  const query = new URLSearchParams(location.search);

  const pliratingId = query.get('pliRatingId');
  const empId = query.get('menteeId');
  const view = query.get('view');
  const isMenteeView = view === 'mentee-view';

  const user = getUserData();
  const isSuperAdminUser = isSuperAdmin(user);

  const tempProjects = useMemo(() => {
    const rated = (ratedProjects || []).map(p => ({
      label: p.projectName,
      value: p.id,
      type: 'Rated',
      projectName: p.projectName,
    }));
    const additional = (additionalProjects || []).map(p => ({
      label: p.projectName,
      value: p.id,
      type: 'Additional',
      projectName: p.projectName,
    }));
    return [...rated, ...additional];
  }, [ratedProjects, additionalProjects]);

  useEffect(() => {
    const { employeeId } = match.params;

    // fetch the profile using the menteeId
    if (pliratingId && view && empId) {
      fetchProfile(empId);
    } else {
      fetchProfile(employeeId);
    }
  }, [fetchProfile, match.params, pliratingId, view, empId]);

  useEffect(() => {
    if (pliratingId && view) {
      fetchPLIRatingById(pliratingId);
    } else if (profile && profile.empId && isSuperAdminUser) {
      fetchExistingPLIData(profile.empId);
    }
  }, [pliratingId, view, profile, isSuperAdminUser]);

  const fetchPLIRatingById = async id => {
    try {
      setLoading(true);
      const response = await request(
        `${API_ENDPOINTS.PLI_RATING_BY_ID}?pliRatingId=${id}`,
        { method: 'GET' },
      );

      if (response && response.status === 1 && response.data) {
        const pliData = response.data;
        setPliRating(pliData);

        const monthName = intToMonthName(pliData.month);
        setSelectedMonth(monthName);

        const newProjectRows = pliData.projectRatings.map(
          (projectRating, index) => {
            const paramScores = projectRating.parameterScores || [];
            const firstParam = paramScores.length > 0 ? paramScores[0] : null;
            const parameter =
              firstParam && firstParam.parameter ? firstParam.parameter : null;
            const paramType =
              parameter && parameter.type ? parameter.type : 'Fixed';

            const projectName =
              projectRating.project && projectRating.project.name
                ? projectRating.project.name
                : '';

            return {
              id: index + 1,
              projectId: projectRating.project.id, // Using project ID as the key
              project: projectRating.project.name,
              projectName,
              projectType: paramType,
              weightage: projectRating.projectWeightage || 100,
            };
          },
        );

        setProjectRows(newProjectRows);

        const newPliDataMap = {};

        pliData.projectRatings.forEach(projectRating => {
          const paramScores = projectRating.parameterScores || [];
          const firstParam = paramScores.length > 0 ? paramScores[0] : null;
          const paramType =
            firstParam && firstParam.parameter && firstParam.parameter.type
              ? firstParam.parameter.type
              : 'Fixed';
          const projectKey = ''
            .concat(projectRating.project.id, '_')
            .concat(paramType);

          const sprintData = {};
          projectRating.parameterScores.forEach(parameterScore => {
            if (
              parameterScore.childScores &&
              parameterScore.childScores.length > 0
            ) {
              parameterScore.childScores.forEach(childScore => {
                const paramName = childScore.childParameter;

                childScore.sprintScores.forEach(sprintScore => {
                  const sprintName = sprintScore.sprintNumber;

                  if (!sprintData[sprintName]) {
                    sprintData[sprintName] = {};
                  }

                  sprintData[sprintName][paramName] = {
                    value: sprintScore.score,
                    weightage: childScore.childParameterWeightage,
                    calculation: childScore.calculation,
                    weightageAverage: childScore.weightageAverage,
                    comment: sprintScore.comment || '',
                  };
                });
              });
            }
          });

          newPliDataMap[projectKey] = sprintData;
        });

        setPliDataMap(newPliDataMap);
        setShowPLIDetails(true);

        const newCommentsMap = {};
        pliData.projectRatings.forEach(projectRating => {
          const paramScores = projectRating.parameterScores || [];
          const firstParam = paramScores.length > 0 ? paramScores[0] : null;
          const paramType =
            firstParam && firstParam.parameter && firstParam.parameter.type
              ? firstParam.parameter.type
              : 'Fixed';
          const projectKey = ''
            .concat(projectRating.project.id, '_')
            .concat(paramType);

          newCommentsMap[projectKey] =
            firstParam && firstParam.comments ? firstParam.comments : '';
        });

        setCommentsMap(newCommentsMap);

        setIds({
          menteeId:
            pliData.mentee && pliData.mentee.employeeId
              ? String(pliData.mentee.employeeId)
              : null,
          mentorId:
            pliData.mentor && pliData.mentor.employeeId
              ? String(pliData.mentor.employeeId)
              : null,
          userId: user && user.id ? String(user.id) : '',
          isSuperAdminUser,
          parameterIds: pliData.projectRatings.reduce((acc, projectRating) => {
            const paramScores = projectRating.parameterScores || [];
            const firstParam = paramScores.length > 0 ? paramScores[0] : null;
            const paramType =
              firstParam && firstParam.parameter && firstParam.parameter.type
                ? firstParam.parameter.type
                : 'Fixed';
            const projectKey = `project_${
              projectRating.project.id
            }_${paramType}`;

            acc[projectKey] =
              firstParam && firstParam._id ? firstParam._id : null;
            return acc;
          }, {}),
        });
      }
    } catch (error) {
      notification.error({
        message: 'Error',
        description: 'Failed to fetch PLI rating data',
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchExistingPLIData = async (employeeId, monthNumber = null) => {
    try {
      setLoading(true);
      let url = `${
        API_ENDPOINTS.PLI_RATING_BY_EMPLOYEE
      }?employeeId=${employeeId}`;
      if (monthNumber) {
        url = `${url}&month=${monthNumber}`;
      }
      const response = await request(url, { method: 'GET' });

      if (
        response &&
        response.status === 1 &&
        response.data &&
        response.data.data &&
        response.data.data.length > 0
      ) {
        const pliData = response.data.data[0];
        setPliRating(pliData);

        const monthName = intToMonthName(pliData.month);
        setSelectedMonth(monthName);

        const newProjectRows = pliData.projectRatings.map(
          (projectRating, index) => {
            const paramScores = projectRating.parameterScores || [];
            const firstParam = paramScores.length > 0 ? paramScores[0] : null;
            const parameter =
              firstParam && firstParam.parameter ? firstParam.parameter : null;
            const paramType =
              parameter && parameter.type ? parameter.type : 'Fixed';

            // eslint-disable-next-line no-unused-vars
            const projectName =
              projectRating.project && projectRating.project.name
                ? projectRating.project.name
                : '';

            return {
              id: index + 1,
              project: projectRating.project.id, // Using project ID as the key
              projectName: projectRating.project.name || '',
              projectType: paramType,
              weightage: projectRating.projectWeightage || 100,
            };
          },
        );

        setProjectRows(newProjectRows);

        const newPliDataMap = {};

        pliData.projectRatings.forEach(projectRating => {
          // eslint-disable-next-line no-unused-vars
          const paramterId =
            projectRating.parameterScores[0].parameterId || null;
          const paramScores = projectRating.parameterScores || [];
          const firstParam = paramScores.length > 0 ? paramScores[0] : null;
          const paramType =
            firstParam && firstParam.parameter && firstParam.parameter.type
              ? firstParam.parameter.type
              : 'Fixed';

          const projectKey = ''
            .concat(projectRating.project.id, '_')
            .concat(paramType);

          const sprintData = {};
          projectRating.parameterScores.forEach(parameterScore => {
            if (
              parameterScore.childScores &&
              parameterScore.childScores.length > 0
            ) {
              parameterScore.childScores.forEach(childScore => {
                const paramName = childScore.childParameter;

                childScore.sprintScores.forEach(sprintScore => {
                  const sprintName = sprintScore.sprintNumber;

                  if (!sprintData[sprintName]) {
                    sprintData[sprintName] = {};
                  }

                  sprintData[sprintName][paramName] = {
                    value: sprintScore.score,
                    weightage: childScore.childParameterWeightage,
                    calculation: childScore.calculation,
                    weightageAverage: childScore.weightageAverage,
                    comment: sprintScore.comment || '',
                  };
                });
              });
            }
          });

          newPliDataMap[projectKey] = sprintData;
        });

        setPliDataMap(newPliDataMap);
        setShowPLIDetails(true);

        const newCommentsMap = {};
        pliData.projectRatings.forEach(projectRating => {
          const paramScores = projectRating.parameterScores || [];
          const firstParam = paramScores.length > 0 ? paramScores[0] : null;
          const paramType =
            firstParam && firstParam.parameter && firstParam.parameter.type
              ? firstParam.parameter.type
              : 'Fixed';
          const projectKey = `project_${projectRating.project.id}_${paramType}`;

          newCommentsMap[projectKey] =
            firstParam && firstParam.comments ? firstParam.comments : '';
        });

        setCommentsMap(newCommentsMap);

        setIds({
          menteeId:
            pliData.mentee && pliData.mentee && pliData.mentee.id
              ? String(pliData.mentee.id)
              : null,
          mentorId:
            pliData.mentor && pliData.mentor && pliData.mentor.id
              ? String(pliData.mentor.id)
              : null,
          userId: user && user.id ? String(user.id) : '',
          isSuperAdminUser,
          parameterIds: pliData.projectRatings.reduce((acc, projectRating) => {
            const paramScores = projectRating.parameterScores || [];
            const firstParam = paramScores.length > 0 ? paramScores[0] : null;
            const paramType =
              firstParam && firstParam.parameter && firstParam.parameter.type
                ? firstParam.parameter.type
                : 'Fixed';
            const projectKey = ''
              .concat(projectRating.id, '_')
              .concat(paramType);

            acc[projectKey] =
              firstParam && firstParam.id ? firstParam.id : null;
            return acc;
          }, {}),
          // Add parameterMappings for direct access in Submit.js
          parameterMappings: pliData.projectRatings.reduce(
            (acc, projectRating) => {
              const paramScores = projectRating.parameterScores || [];
              const firstParam = paramScores.length > 0 ? paramScores[0] : null;
              const parameterId =
                firstParam && firstParam.parameterId
                  ? firstParam.parameterId
                  : null;

              // Use project.id as the key for direct access in Submit.js
              if (
                projectRating.project &&
                projectRating.project.id &&
                parameterId
              ) {
                acc[projectRating.project.id] = parameterId;
              }
              return acc;
            },
            {},
          ),
        });
        setIsSuperAdminOverridden(pliData.superAdminOverride);

        // Use the PLI_RATING_BY_EMPLOYEE endpoint to check if PLI exists
        const hasExistingPLI =
          response?.status === 1 &&
          response?.data?.status === 1 &&
          Array.isArray(response.data.data) &&
          response.data.data.length > 0;

        // Set PLI existence state
        setPliExists(hasExistingPLI);

        // Check if PLI is frozen and store PLI rating data
        if (hasExistingPLI && response.data.data[0]) {
          const fetchedIsFrozen = response.data.data[0].isFrozen || false;
          setIsFrozen(fetchedIsFrozen);
          setPliRatingId(response.data.data[0]._id || null);
          setPliRating(response.data.data[0]);
          setIsSuperAdminOverridden(response.data.data[0].superAdminOverride);
        } else {
          setIsFrozen(false);
          setPliRatingId(null);
          setPliRating(null);
          setIsSuperAdminOverridden(false);
        }
      }
    } catch (error) {
      notification.error({
        message: 'Error',
        description: 'Failed to fetch existing PLI data',
      });
    } finally {
      setLoading(false);
    }
  };

  const mappedProfile = {
    name: profile && profile.name,
    employeeId:
      profile && (profile.empId !== undefined ? String(profile.empId) : ''),
    department: profile && profile.department,
    reportingManager: profile && profile.reportingManager,
    managerId:
      profile &&
      (profile.reportingManagerId !== undefined
        ? String(profile.reportingManagerId)
        : ''),
    doj: profile && profile.doj,
    pliDuration: profile && profile.pliDuration,
    role: profile && profile.role,
  };

  const handleMonthChange = monthInWords => {
    setSelectedMonth(monthInWords);
    const monthNumber = monthNameToInt(monthInWords);
    // Reset states
    setPliDataMap({});
    setShowPLIDetails(false); // Reset showPLIDetails to false
    // Reset PLI status flags
    setPliExists(false);
    setIsFrozen(false);
    setIsSuperAdminOverridden(false);

    const menteeId = profile && profile.empId;
    if (!menteeId) return;

    // If month is not valid, don't proceed
    if (!monthNumber) {
      notification.error({
        message: 'Invalid Month',
        description: 'Please select a valid month.',
      });
      return;
    }

    setLoading(true);

    // Special handling for super admin users
    if (isSuperAdminUser) {
      // Call getPliRatingByEmployee API with month parameter
      const url = `${
        API_ENDPOINTS.PLI_RATING_BY_EMPLOYEE
      }?employeeId=${menteeId}&month=${monthNumber}`;

      request(url, { method: 'GET' })
        .then(response => {
          if (
            response &&
            response.status === 1 &&
            response.data &&
            response.data.data &&
            response.data.data.length > 0
          ) {
            const pliData = response.data.data[0];

            // Extract project rows from PLI data
            const newProjectRows = pliData.projectRatings.map(
              (projectRating, index) => {
                const paramScores = projectRating.parameterScores || [];
                const firstParam =
                  paramScores.length > 0 ? paramScores[0] : null;
                const parameter =
                  firstParam && firstParam.parameter
                    ? firstParam.parameter
                    : null;
                const paramType =
                  parameter && parameter.type ? parameter.type : 'Fixed';

                const projectName =
                  projectRating.project && projectRating.project.name
                    ? projectRating.project.name
                    : '';

                return {
                  id: index + 1,
                  project: projectRating.project.name,
                  projectName,
                  projectType: paramType,
                  weightage: projectRating.projectWeightage || 100,
                };
              },
            );

            setProjectRows(newProjectRows);

            // Process PLI data for display
            const newPliDataMap = {};

            pliData.projectRatings.forEach(projectRating => {
              const paramScores = projectRating.parameterScores || [];
              const firstParam = paramScores.length > 0 ? paramScores[0] : null;
              const paramType =
                firstParam && firstParam.parameter && firstParam.parameter.type
                  ? firstParam.parameter.type
                  : 'Fixed';
              const projectKey = ''
                .concat(projectRating.project.id, '_')
                .concat(paramType);

              const sprintData = {};
              projectRating.parameterScores.forEach(parameterScore => {
                if (
                  parameterScore.childScores &&
                  parameterScore.childScores.length > 0
                ) {
                  parameterScore.childScores.forEach(childScore => {
                    const paramName = childScore.childParameter;

                    childScore.sprintScores.forEach(sprintScore => {
                      const sprintName = sprintScore.sprintNumber;

                      if (!sprintData[sprintName]) {
                        sprintData[sprintName] = {};
                      }

                      sprintData[sprintName][paramName] = {
                        value: sprintScore.score,
                        weightage: childScore.childParameterWeightage,
                        calculation: childScore.calculation,
                        weightageAverage: childScore.weightageAverage,
                        comment: sprintScore.comment || '',
                      };
                    });
                  });
                }
              });

              newPliDataMap[projectKey] = sprintData;
            });

            setPliDataMap(newPliDataMap);
            setShowPLIDetails(true);

            // Process comments
            const newCommentsMap = {};
            pliData.projectRatings.forEach(projectRating => {
              const paramScores = projectRating.parameterScores || [];
              const firstParam = paramScores.length > 0 ? paramScores[0] : null;
              const paramType =
                firstParam && firstParam.parameter && firstParam.parameter.type
                  ? firstParam.parameter.type
                  : 'Fixed';
              const projectKey = ''
                .concat(projectRating.project.id, '_')
                .concat(paramType);

              newCommentsMap[projectKey] =
                firstParam && firstParam.comments ? firstParam.comments : '';
            });

            setCommentsMap(newCommentsMap);

            // Set IDs
            setIds({
              menteeId:
                pliData.mentee && pliData.mentee.employeeId
                  ? String(pliData.mentee.employeeId)
                  : null,
              mentorId:
                pliData.mentor && pliData.mentor.employeeId
                  ? String(pliData.mentor.employeeId)
                  : null,
              userId: user && user.id ? String(user.id) : '',
              isSuperAdminUser,
              parameterIds: pliData.projectRatings.reduce(
                (acc, projectRating) => {
                  const paramScores = projectRating.parameterScores || [];
                  const firstParam =
                    paramScores.length > 0 ? paramScores[0] : null;
                  const paramType =
                    firstParam &&
                    firstParam.parameter &&
                    firstParam.parameter.type
                      ? firstParam.parameter.type
                      : 'Fixed';
                  const projectKey = ''
                    .concat(projectRating.project._id, '_')
                    .concat(paramType);

                  acc[projectKey] =
                    firstParam && firstParam._id ? firstParam._id : null;
                  return acc;
                },
                {},
              ),
            });
            setIsSuperAdminOverridden(pliData.superAdminOverride);
          } else {
            // If no PLI data found, reset to empty state
            setProjects([]);
            setProjectRows([
              {
                id: 1,
                project: null,
                projectType: null,
                weightage: '',
                projectName: null,
              },
            ]);
            notification.info({
              message: 'No PLI Data',
              description: `No PLI data found for ${
                profile.name
              } in ${monthInWords} ${new Date().getFullYear()}.`,
            });
          }
          setLoading(false);
        })
        .catch(() => {
          notification.error({
            message: 'Error',
            description: 'Failed to fetch PLI rating data',
          });
          setLoading(false);
        });
    } else {
      const url = `${API_ENDPOINTS.PLI_PROJECTS_BY_LABEL}?menteeLabel=${
        profile.label
      }&month=${monthNumber}&year=${new Date().getFullYear()}`;

      request(url, { method: 'GET' })
        .then(json => {
          if (json.success && json.data) {
            setProjects([
              ...json.data.ratedProjects,
              ...json.data.additionalProjects,
            ]);

            if (json.data.ratedProjects && json.data.ratedProjects.length > 0) {
              const ratedProjectRows = json.data.ratedProjects.map(
                (project, index) => ({
                  id: index + 1,
                  project: project._id,
                  projectName: project.projectName,
                  projectType: project.projectType || 'Fixed',
                  weightage: '',
                }),
              );
              setProjectRows(ratedProjectRows);
            } else {
              setProjectRows([]);
            }
          } else {
            setProjects([]);
            setProjectRows([
              {
                id: 1,
                project: null,
                projectType: null,
                weightage: '',
                projectName: null,
              },
            ]);
          }
          setLoading(false);
        })
        .catch(() => {
          notification.error({
            message: 'Error',
            description: 'Failed to fetch data',
          });
          setLoading(false);
        });
    }
  };

  const handleProjectChange = (value, rowId, option = {}) => {
    const projectSelected = tempProjects.find(proj => proj.value === value);
    let projectName = projectSelected ? projectSelected.projectName : null;
    if (option && option.label) {
      projectName = option.label;
    }

    setProjectRows(prev =>
      prev.map(row => {
        if (row.id === rowId) {
          return {
            ...row,
            project: value,
            projectName,
            projectType: projectSelected ? projectSelected.type : 'Fixed',
            isAdditional: true,
          };
        }
        return row;
      }),
    );
  };

  const handleProjectTypeChange = (value, rowId) => {
    setProjectRows(prev =>
      prev.map(row => {
        if (row.id === rowId) {
          return {
            ...row,
            projectType: value,
          };
        }
        return row;
      }),
    );
  };

  const handleWeightageChange = (value, rowId) => {
    const trimmed = String(value).trim();
    const newValue = Number(trimmed);

    const otherRowsTotal = projectRows
      .filter(row => row.id !== rowId)
      .reduce((sum, row) => sum + (Number(row.weightage) || 0), 0);

    if (otherRowsTotal + newValue > 100) {
      notification.error({
        message: 'Invalid Weightage',
        description: 'Total weightage cannot exceed 100%',
      });
    }

    setProjectRows(prev =>
      prev.map(row => (row.id === rowId ? { ...row, weightage: value } : row)),
    );
  };

  const handleAddRow = () => {
    setProjectRows(prev => [
      ...prev,
      {
        id: prev.length > 0 ? Math.max(...prev.map(r => r.id)) + 1 : 1,
        project: null,
        projectType: null,
        weightage: '',
        projectName: null,
        isAdditional: true,
      },
    ]);
  };

  const handleRemoveRow = rowId => {
    if (projectRows.length <= 1) {
      notification.info({
        message: 'Cannot Remove',
        description: 'At least one project row must remain.',
      });
      return;
    }

    setProjectRows(prev => prev.filter(row => row.id !== rowId));
  };

  const handleSync = async () => {
    const hasInvalid = projectRows.some(row => {
      const trimmed = String(row.weightage).trim();
      const value = Number(trimmed);
      return !trimmed || Number.isNaN(value) || value <= 0;
    });

    if (hasInvalid) {
      notification.error({
        message: 'Invalid Input',
        description: 'All projects must have valid weightage > 0.',
      });
      return Promise.reject(new Error('Invalid weightage'));
    }

    const total = projectRows.reduce(
      (sum, row) => sum + (Number(row.weightage) || 0),
      0,
    );

    if (total !== 100) {
      notification.warning({
        message: 'Weightage Mismatch',
        description: `Total weightage must equal 100%. Current: ${total}%`,
      });
      return Promise.reject(new Error('Invalid total weightage'));
    }

    setLoading(true);
    const newMap = {};
    const monthNumber = monthNameToInt(selectedMonth);

    // If month is not valid, don't proceed
    if (!monthNumber) {
      notification.error({
        message: 'Invalid Month',
        description: 'Please select a valid month.',
      });
      setLoading(false);
      return Promise.reject(new Error('Invalid month'));
    }

    try {
      const projectsToProcess = projectRows.filter(row => row.project);
      await Promise.all(
        projectsToProcess.map(async row => {
          try {
            const url = `${
              API_ENDPOINTS.PROJECT_SPRINT_DATA
            }?project=${encodeURIComponent(
              row.projectName,
            )}&month=${monthNumber}&year=${new Date().getFullYear()}&employeeName=${
              profile.label
            }&projectType=${row.projectType || 'Fixed'}`;

            const json = await request(url, {
              method: 'GET',
            });

            const projectKey1 = `${row.project}_${row.projectType || 'Fixed'}`;

            setIds(prevIds => ({
              menteeId: json.data.menteeId || prevIds.menteeId,
              mentorId: json.data.mentorId || prevIds.mentorId,
              isSuperAdminUser,
              parameterIds: {
                ...prevIds.parameterIds,
                [projectKey1]: json.data.parameterId,
              },
            }));

            const sprintData = {};
            if (json && json.data && Array.isArray(json.data.data)) {
              const allSprints = new Set();
              json.data.data.forEach(item => {
                if (
                  item.pliParameter &&
                  Array.isArray(item.pliParameter.sprintwiseScores)
                ) {
                  item.pliParameter.sprintwiseScores.forEach(sprint => {
                    if (sprint.sprintname) {
                      allSprints.add(sprint.sprintname);
                    }
                  });
                }
              });

              allSprints.forEach(sprintName => {
                sprintData[sprintName] = {};
              });

              json.data.data.forEach(item => {
                if (item.pliParameter) {
                  const { name: paramName, weightage } = item.pliParameter;
                  if (paramName) {
                    if (Array.isArray(item.pliParameter.sprintwiseScores)) {
                      const scores = item.pliParameter.sprintwiseScores
                        .map(sprint => sprint.score)
                        .filter(score => score !== undefined && score !== null);

                      let averageScore = 0;

                      if (scores.length > 0) {
                        const totalScore = scores.reduce(
                          (sum, score) => sum + Number(score),
                          0,
                        );
                        averageScore = totalScore / scores.length;
                      } else {
                        averageScore = 0;
                      }

                      const calculatedAverage = Number(averageScore.toFixed(2));

                      item.pliParameter.sprintwiseScores.forEach(sprint => {
                        const sprintName = sprint.sprintname;
                        if (sprintName && sprintData[sprintName]) {
                          sprintData[sprintName][paramName] = {
                            value:
                              sprint.score !== undefined ? sprint.score : 'N/A',
                            weightage,
                            calculation: calculatedAverage,
                            weightageAverage: Number(
                              (calculatedAverage * (weightage || 0)).toFixed(2),
                            ),
                            comment: sprint.comment || '',
                          };
                        }
                      });
                    }
                  }
                }
              });
            }

            const projectKey = `${row.project}_${row.projectType || 'Fixed'}`;
            newMap[projectKey] = sprintData;
          } catch (err) {
            notification.error({
              message: 'Fetch Error',
              description: `Failed to fetch PLI data for ${row.projectName}`,
            });
            throw err;
          }
        }),
      );

      setPliDataMap(newMap);
      setShowPLIDetails(true);

      // Check if a PLI rating exists after syncing
      const menteeId = profile && profile.empId;
      if (menteeId && monthNumber) {
        const checkPliUrl = `${
          API_ENDPOINTS.PLI_RATING_BY_EMPLOYEE
        }?employeeId=${menteeId}&month=${monthNumber}&year=${new Date().getFullYear()}`;

        const response = await request(checkPliUrl, { method: 'GET' });
        const hasExistingPLI =
          response?.status === 1 &&
          response?.data?.status === 1 &&
          Array.isArray(response.data.data) &&
          response.data.data.length > 0;

        setPliExists(hasExistingPLI);

        if (hasExistingPLI && response.data.data[0]) {
          const fetchedIsFrozen = response.data.data[0].isFrozen || false;
          setIsFrozen(fetchedIsFrozen);
          setPliRatingId(response.data.data[0]._id || null);
          setPliRating(response.data.data[0]);
          setIsSuperAdminOverridden(response.data.data[0].superAdminOverride);
        } else {
          setIsFrozen(false);
          setPliRatingId(null);
          setPliRating(null);
          setIsSuperAdminOverridden(false);
        }
      }

      setLoading(false);
      return Promise.resolve();
    } catch (error) {
      setLoading(false);
      return Promise.reject(error);
    }
  };

  const projectOptions = projectRows.map(p => {
    const { projectId, projectName, type } = p;
    return {
      label: projectName,
      value: projectId,
      type: type || '',
      projectName,
    };
  });
  const getProjectTitle = projectId => {
    // First check in projectOptions
    const projectFromOptions = projectOptions.find(p => p.value === projectId);
    if (projectFromOptions) {
      return projectFromOptions.label;
    }

    // If not found, check in projectRows
    const projectFromRows = projectRows.find(p => p.project === projectId);
    if (projectFromRows) {
      return projectFromRows.projectName;
    }

    // If still not found, return the projectId itself as it might be the name
    return projectId;
  };

  const onPLISubmitted = useCallback(() => {
    setScoresModified(false);
    // Check if the current user is a Super Admin
    if (isSuperAdminUser) {
      // Super Admin reloads the page (existing behavior)
      window.location.reload();
    } else {
      // For mentor users, re-fetch data first
      const menteeId = profile && profile.empId;
      const monthNumber = monthNameToInt(selectedMonth);

      if (menteeId && monthNumber) {
        // First fetch the updated data
        fetchExistingPLIData(menteeId, monthNumber).then(() => {
          // Then sync to ensure proper data format
          handleSync();
        });
      } else {
        window.location.reload();
      }
    }
  }, [
    setScoresModified,
    isSuperAdminUser,
    profile,
    selectedMonth,
    fetchExistingPLIData,
    handleSync,
  ]);

  // Handle modal confirmation
  const handleScoreModalConfirm = useCallback(
    (newValue, comment) => {
      if (modalData) {
        const {
          projectKey,
          sprint,
          parameter,
          isCalculation,
          isWeightageAverage,
        } = modalData;

        // Calculate oldVal based on the type of value being edited
        let oldVal;
        if (isCalculation) {
          oldVal = pliDataMap[projectKey]?.[sprint]?.[parameter]?.calculation;
        } else if (isWeightageAverage) {
          oldVal =
            pliDataMap[projectKey]?.[sprint]?.[parameter]?.weightageAverage;
        } else {
          oldVal = pliDataMap[projectKey]?.[sprint]?.[parameter]?.value;
        }

        // Only update if the value has actually changed
        if (String(newValue) !== String(oldVal)) {
          // Update the pliDataMap with the new value
          setPliDataMap(prevMap => {
            const newMap = JSON.parse(JSON.stringify(prevMap));
            if (newMap[projectKey] && newMap[projectKey][sprint]) {
              if (isCalculation) {
                newMap[projectKey][sprint][parameter].calculation = newValue;
              } else if (isWeightageAverage) {
                newMap[projectKey][sprint][
                  parameter
                ].weightageAverage = newValue;
              } else {
                newMap[projectKey][sprint][parameter].value = newValue;
              }
              // Store the comment directly in the sprintScore object
              newMap[projectKey][sprint][parameter].comment = comment; // Update comment field
            }
            return newMap;
          });

          // Mark scores as modified if the change is from an editable state
          if (
            !isMenteeView &&
            !isSuperAdminOverridden &&
            (isSuperAdminUser || !isFrozen)
          ) {
            setScoresModified(true);
          }
        } else if (comment) {
          // Handle case where value didn't change but comment was added
          setPliDataMap(prevMap => {
            const newMap = JSON.parse(JSON.stringify(prevMap));
            if (newMap[projectKey] && newMap[projectKey][sprint]) {
              // Update the comment field even if the score didn't change
              newMap[projectKey][sprint][parameter].comment = comment;
            }
            return newMap;
          });
        }
      }
      setIsModalVisible(false);
      setModalData(null);
    },
    [
      modalData,
      pliDataMap,
      isMenteeView,
      isSuperAdminOverridden,
      isSuperAdminUser,
      isFrozen,
      setScoresModified,
    ],
  );

  // Handle modal cancellation
  const handleScoreModalCancel = useCallback(() => {
    setIsModalVisible(false);
    setModalData(null);
  }, []);

  return (
    <div>
      <PageHeader title="Employee Profile" />
      <EmployeeDetails
        profile={mappedProfile}
        selectedMonth={selectedMonth}
        projectRows={projectRows}
        loading={loading}
        showPLIDetails={showPLIDetails}
        tempProjects={projectOptions}
        handleMonthChange={handleMonthChange}
        handleProjectChange={handleProjectChange}
        handleProjectTypeChange={handleProjectTypeChange}
        handleWeightageChange={handleWeightageChange}
        handleAddRow={handleAddRow}
        handleRemoveRow={handleRemoveRow}
        handleSync={handleSync}
        getProjectType={key1 => getProjectTitle(key1).split(' - ')[1]}
        isMenteeView={isMenteeView}
        isSuperAdminUser={isSuperAdminUser}
      />
      {showPLIDetails && (
        <>
          {/* Show Finalized by Super Admin message */}
          {isSuperAdminOverridden && (
            <StatusRibbon>
              <div
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#fff2f0',
                  border: '1px solid #ffccc7',
                  borderRadius: '4px',
                  marginBottom: '16px',
                }}
              >
                <p style={{ margin: 0, color: '#ff4d4f' }}>
                  <b>Note:</b> This PLI has been finalized by a Super Admin and
                  cannot be modified further.
                </p>
              </div>
            </StatusRibbon>
          )}
          {/* eslint-disable indent */}
          {/* Show Submitted message only if not frozen or finalized by Super Admin */}
          {pliExists &&
            !isFrozen &&
            !(isSuperAdminUser && isSuperAdminOverridden) && (
              <StatusRibbon>
                <div
                  style={{
                    padding: '8px 16px',
                    backgroundColor: '#e6f7ff',
                    border: '1px solid #91d5ff',
                    borderRadius: '4px',
                    marginBottom: '16px',
                  }}
                >
                  <p style={{ margin: 0, color: '#1890ff' }}>
                    <b>Note:</b> PLI ratings for {selectedMonth} have already
                    been submitted. You can edit and re-submit until frozen.
                  </p>
                </div>
              </StatusRibbon>
            )}
          {/* Show frozen message only if frozen by mentor and not finalized by Super Admin - ONLY FOR NON-SUPER ADMINS */}
          {pliExists &&
            isFrozen &&
            !isSuperAdminOverridden &&
            !isSuperAdminUser && (
              <StatusRibbon>
                <div
                  style={{
                    padding: '8px 16px',
                    backgroundColor: '#fff2f0',
                    border: '1px solid #ffccc7',
                    borderRadius: '4px',
                    marginBottom: '16px',
                  }}
                >
                  <p style={{ margin: 0, color: '#ff4d4f' }}>
                    <b>Note:</b> This PLI rating has been frozen and cannot be
                    edited.
                  </p>
                </div>
              </StatusRibbon>
            )}
          {/* Show specific message for Super Admin when PLI is frozen by mentor */}
          {isSuperAdminUser &&
            pliExists &&
            isFrozen &&
            !isSuperAdminOverridden && (
              <StatusRibbon>
                <div
                  style={{
                    padding: '8px 16px',
                    backgroundColor: '#e6f7ff',
                    border: '1px solid #91d5ff',
                    borderRadius: '4px',
                    marginBottom: '16px',
                  }}
                >
                  <p style={{ margin: 0, color: '#1890ff' }}>
                    <b>Note:</b> PLI rating has been frozen by mentor but can be
                    edited by you.
                  </p>
                </div>
              </StatusRibbon>
            )}
          {/* eslint-enable indent */}
          {Object.entries(pliDataMap).map(([projectKey, pliData]) => {
            const sprints = Object.keys(pliData);

            const [projectId, projectType] = projectKey.split('_');
            const projectTitle = getProjectTitle(projectId);

            return (
              <Row key={projectKey} style={{ marginBottom: '20px' }}>
                <Col span={24}>
                  <DetailCard title={`${projectTitle} - ${projectType}`}>
                    <DetailRow>
                      <Col span={6}>
                        <Label>Parameters</Label>
                      </Col>
                      {sprints.map(s => (
                        <Col key={s} span={3}>
                          <HeaderCell>{s}</HeaderCell>
                        </Col>
                      ))}
                      <Col span={3}>
                        <HeaderCell>Calculation</HeaderCell>
                      </Col>
                      <Col span={3}>
                        <HeaderCell>Weightage Avg</HeaderCell>
                      </Col>
                    </DetailRow>
                    {Object.keys(pliData[sprints[0]] || {}).map(param => (
                      <DetailRow key={param}>
                        <Col span={6}>
                          <Label>{param}</Label>
                        </Col>
                        {sprints.map(s => (
                          <Col key={`${s}-${param}`} span={3}>
                            <div style={{ position: 'relative' }}>
                              <Input
                                style={{ width: '100%' }}
                                value={
                                  pliData[s] &&
                                  pliData[s][param] &&
                                  pliData[s][param].value !== undefined
                                    ? pliData[s][param].value
                                    : ''
                                }
                                // Disable based on view, frozen status, and Super Admin override
                                disabled={
                                  isMenteeView ||
                                  isSuperAdminOverridden ||
                                  (!isSuperAdminUser && isFrozen)
                                }
                                placeholder="Enter value"
                                // Add onClick to open modal if editable
                                onClick={() => {
                                  if (
                                    !isMenteeView &&
                                    !isSuperAdminOverridden &&
                                    (isSuperAdminUser || !isFrozen)
                                  ) {
                                    setModalData({
                                      projectKey,
                                      sprint: s,
                                      parameter: param,
                                      originalValue: pliData[s][param]?.value,
                                      currentValue: pliData[s][param]?.value,
                                    });
                                    setIsModalVisible(true);
                                  }
                                }}
                                readOnly // Make input read-only to force modal interaction
                              />
                              {/* Show icon if a comment string exists for this sprint score */}
                              {pliData[s]?.[param]?.comment && (
                                <InfoCircleOutlined
                                  style={{
                                    position: 'absolute',
                                    right: '8px',
                                    top: '50%',
                                    transform: 'translateY(-50%)',
                                    cursor: 'pointer',
                                    color: '#1890ff',
                                  }}
                                  onClick={() => {
                                    setModalData({
                                      projectKey,
                                      sprint: s,
                                      parameter: param,
                                      originalValue: pliData[s][param]?.value,
                                      currentValue: pliData[s][param]?.value,
                                      isReadOnly: true,
                                      comments: pliData[s][param]?.comment, // Pass the comment string
                                    });
                                    setIsModalVisible(true);
                                  }}
                                />
                              )}
                            </div>
                          </Col>
                        ))}
                        <Col span={3}>
                          <Input
                            style={{ width: '100%' }}
                            value={
                              pliData[sprints[0]] &&
                              pliData[sprints[0]][param] &&
                              pliData[sprints[0]][param].calculation !==
                                undefined
                                ? pliData[sprints[0]][param].calculation
                                : ''
                            }
                            disabled
                            placeholder="Auto-calculated"
                          />
                        </Col>
                        <Col span={3}>
                          <Input
                            style={{ width: '100%' }}
                            value={
                              pliData[sprints[0]] &&
                              pliData[sprints[0]][param] &&
                              pliData[sprints[0]][param].weightageAverage !==
                                undefined
                                ? pliData[sprints[0]][param].weightageAverage
                                : ''
                            }
                            disabled
                            placeholder="Auto-calculated"
                          />
                        </Col>
                      </DetailRow>
                    ))}
                    <TextAreaContainer>
                      <Label>Comments</Label>

                      <StyledTextArea
                        value={commentsMap[projectKey] || ''}
                        onChange={e =>
                          setCommentsMap(prev => ({
                            ...prev,
                            [projectKey]: e.target.value,
                          }))
                        }
                        disabled={
                          !editingMap[projectKey] || isSuperAdminOverridden
                        }
                        placeholder="Enter your comments for this project..."
                      />
                    </TextAreaContainer>
                    <SaveButton
                      type="primary"
                      onClick={() => {
                        notification.success({
                          message: `Comment Saved for ${projectTitle}`,
                        });
                        setEditingMap(prev => ({
                          ...prev,
                          [projectKey]: false,
                        }));
                      }}
                      disabled={isMenteeView || isSuperAdminOverridden}
                    >
                      Save
                    </SaveButton>
                  </DetailCard>
                </Col>
              </Row>
            );
          })}
          {Object.keys(pliDataMap).length > 0 && (
            <Row style={{ marginTop: '20px', marginBottom: '20px' }}>
              <Col span={24} style={{ textAlign: 'center' }} />
            </Row>
          )}
        </>
      )}
      <EmployeeProfileProvider
        value={{
          pliDataMap,
          profile,
          selectedMonth,
          commentsMap,
          onPLISubmitted,
        }}
      >
        {showPLIDetails && selectedMonth && !isMenteeView && (
          <>
            {(!isFrozen || (isSuperAdminUser && !isSuperAdminOverridden)) && (
              <>
                <Submit
                  ids={{
                    ...ids,
                    menteeId: profile.menteeId || profile.empId,
                    pliRatingId,
                    isSuperAdminUser,
                  }}
                  // Enable if scores modified OR if this is first-time submission (no PLI exists)
                  disabled={!scoresModified && pliExists}
                />

                <FreezePLI
                  ids={{
                    ...ids,
                    menteeId: profile.menteeId || profile.empId,
                    pliRatingId,
                    isSuperAdminUser,
                  }}
                  mentorId={profile.mentorId || user._id}
                  menteeId={profile.menteeId || profile.empId}
                  isPLIFrozen={isFrozen}
                  existingPliRatingId={pliRatingId}
                  // Enabled if not scores modified and PLI exists
                  disabled={scoresModified || !pliExists}
                  // eslint-disable-next-line react/jsx-no-bind
                  onPLISubmitted={onPLISubmitted.bind(this)}
                />

                {scoresModified && ( // Notification when scores are modified
                  <div
                    style={{
                      margin: '20px 0',
                      textAlign: 'center',
                      color: '#ff4d4f',
                      backgroundColor: '#fff2f0',
                      padding: '8px 16px',
                      border: '1px solid #ffccc7',
                      borderRadius: '4px',
                    }}
                  >
                    <b>Note:</b> Changes have been made to the scores. Please
                    submit the changes before freezing.
                  </div>
                )}
              </>
            )}
          </>
        )}
        {showPLIDetails && isMenteeView && <RaiseQuery />}
      </EmployeeProfileProvider>
      {/* Add the ScoreEditModal component */}
      <ScoreEditModal
        visible={isModalVisible}
        onCancel={handleScoreModalCancel}
        onConfirm={handleScoreModalConfirm}
        sprintName={modalData?.sprint || ''}
        parameterName={modalData?.parameter || ''}
        originalValue={modalData?.originalValue}
        currentValue={modalData?.currentValue}
        isReadOnly={modalData?.isReadOnly}
        comments={modalData?.comments}
        isSuperAdmin={isSuperAdminUser}
      />
    </div>
  );
};

EmployeeProfile.propTypes = {
  fetchProfile: PropTypes.func.isRequired,
  match: PropTypes.object.isRequired,
  profileState: PropTypes.shape({
    loading: PropTypes.bool,
    error: PropTypes.object,
    profile: PropTypes.object,
    projectsLoading: PropTypes.bool,
    projectsError: PropTypes.object,
    ratedProjects: PropTypes.array,
    additionalProjects: PropTypes.array,
  }),
};

const mapStateToProps = createStructuredSelector({
  profileState: makeSelectEmployeeProfile(),
});

const mapDispatchToProps = dispatch => ({
  fetchProfile: employeeId => dispatch(fetchEmployeeProfile(employeeId)),
  fetchProjects: (employeeId, month) =>
    dispatch(fetchEmployeeProjects(employeeId, month)),
});

export default compose(
  connect(
    mapStateToProps,
    mapDispatchToProps,
  ),
)(EmployeeProfile);
