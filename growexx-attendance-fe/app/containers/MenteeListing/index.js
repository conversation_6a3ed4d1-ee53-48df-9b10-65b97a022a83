/* eslint-disable react/no-unused-state */
/* eslint-disable indent */
/* eslint-disable no-underscore-dangle */
/**
 *
 * MenteeListing
 *
 */

import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import request from 'utils/request';
import { debounce } from 'lodash';
import { createStructuredSelector } from 'reselect';
import { compose } from 'redux';
import { reduxForm, change } from 'redux-form';
import useInjectReducer from 'utils/injectReducer';
import * as formValidations from 'utils/formValidations';
import { getUserData } from 'utils/Helper';
import { FormattedMessage } from 'react-intl';
import {
  PageHeader,
  Table,
  notification,
  Space,
  Tag,
  Button,
  Modal,
  Input,
  Select,
  Tooltip,
} from 'antd';
import { SearchOutlined, EyeFilled, EditOutlined } from '@ant-design/icons';
import messages from './messages';
import makeSelectMenteeList from './selectors';
import reducer from './reducer';
import * as actions from './actions';
import { MENTEE_LIST_FORM_KEY } from './constants';
import EditMentee from './EditMentee';
import {
  API_ENDPOINTS,
  DEFAULT_LIMIT,
  DEFAULT_PAGE,
  GET_SORT_ORDER,
  SORTING,
  ROLES,
} from '../constants';
import { setDeepLinkURL } from '../../utils/functions';
import { StyledTableContainer } from '../UserListing/StyledUserListing';

const { Option } = Select;

const menteeTableProps = {
  bordered: true,
  size: 'middle',
  showHeader: true,
};

export class MenteeListing extends Component {
  constructor(props) {
    super(props);
    this.state = {
      menteeList: [],
      loading: false,
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      activeTab: 'mentees',
    };
    this._isMounted = false;
  }

  componentDidMount() {
    this._isMounted = true;
    const query = new URLSearchParams(window.location.search);

    const menteeStatus = query.get('status');
    const pageSize = query.get('pageSize');
    const searchText = query.get('searchText');
    const currentPage = query.get('currentPage');

    const queryParamsObj = {};

    if (menteeStatus !== null) {
      queryParamsObj.isActive = menteeStatus;
      queryParamsObj.filterApplied = true;
      this.setState({ activeFilter: menteeStatus });
    }

    if (pageSize || currentPage) {
      queryParamsObj.pagination = {
        pageSize: pageSize ? parseInt(pageSize, 10) : DEFAULT_LIMIT,
        current: currentPage ? parseInt(currentPage, 10) : DEFAULT_PAGE,
      };
      queryParamsObj.filterApplied = true;
      this.setState({
        pagination: {
          pageSize: pageSize ? parseInt(pageSize, 10) : DEFAULT_LIMIT,
          current: currentPage ? parseInt(currentPage, 10) : DEFAULT_PAGE,
        },
      });
    }

    if (searchText !== null) {
      queryParamsObj.searchValue = searchText;
      queryParamsObj.filterApplied = true;
      this.setState({ searchValue: searchText });
    }

    this.getMenteeList(queryParamsObj);
  }

  componentWillUnmount() {
    this._isMounted = false;
  }

  getMenteeList = ({
    pagination: newPagination,
    sortType: newSortType,
    sortBy: newSortBy,
    isActive,
    searchValue: newSearchValue,
    filterApplied,
  } = {}) => {
    let { pagination, sortType, sortBy, searchValue } = this.state;
    const { activeFilter } = this.state;
    pagination = newPagination || pagination;
    sortType = newSortType || sortType || 'asc';
    sortBy = newSortBy || sortBy || 'firstName';
    searchValue = newSearchValue || searchValue || '';
    if (sortBy === 'firstName') {
      sortBy = 'name';
    }

    this.setState(
      {
        isPaginationLoading: true,
      },
      () => {
        let activeString = '';
        const activeState = isActive || activeFilter;
        if (activeState !== '-1' && activeState !== undefined) {
          activeString = `&isActive=${isActive || activeFilter}`;
        }
        const URL = `${API_ENDPOINTS.GET_MENTEE}?page=${
          pagination.current
        }&limit=${
          pagination.pageSize
        }&sort=${sortType}&sortBy=${sortBy}&name=${encodeURIComponent(
          searchValue,
        )}${activeString}`;

        request(URL, {
          method: 'GET',
        })
          .then(res => {
            if (!this._isMounted) return;

            const { data } = res;

            if (data && typeof data === 'object' && data.docs) {
              const { page, totalDocs, limit, docs } = data;
              this.setState({
                isPaginationLoading: false,
                menteeList: docs,
                pagination: {
                  current: page,
                  pageSize: limit,
                  total: totalDocs,
                },
                sortType,
                filterApplied,
              });
            } else if (Array.isArray(data)) {
              this.setState({
                isPaginationLoading: false,
                menteeList: data,
                pagination: {
                  ...pagination,
                  total: data.length,
                },
                sortType,
                filterApplied,
              });
            } else {
              this.setState({
                isPaginationLoading: false,
                menteeList: [],
                pagination: {
                  ...pagination,
                  total: 0,
                },
                sortType,
                filterApplied,
              });
            }
          })
          .catch(() => {
            if (!this._isMounted) return;

            // console.error('Error fetching mentees:', error);
            notification.error({
              message: 'Error',
              description: 'Failed to load mentees. Please try again.',
            });
            this.setState({
              isPaginationLoading: false,
            });
          });
      },
    );
  };

  tableColumns = [
    {
      title: 'Employee Id',
      dataIndex: 'employeeId',
      key: 'employeeId',
      fixed: 'left',
      sorter: true,
      sortDirections: ['ascend', 'descend', 'ascend'],
      width: '15%',
    },
    {
      title: 'Name',
      dataIndex: 'firstName',
      key: 'name',
      sorter: true,
      width: '25%',
      sortDirections: ['ascend', 'descend', 'ascend'],
      fixed: 'left',
      render: (firstName, record) => (
        <span style={{ cursor: 'pointer' }}>
          {`${firstName} ${record.lastName || ''}`}
        </span>
      ),
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      width: '30%',
    },
    {
      title: 'Label',
      dataIndex: 'label',
      key: 'label',
      width: '25%',
      render: label => (
        <>
          {label && (
            <Tag color="purple" key={label}>
              {label}
            </Tag>
          )}
        </>
      ),
    },
    {
      title: 'Actions',
      dataIndex: 'action',
      key: 'action',
      width: '15%',
      fixed: 'right',
      render: (_action, record) => (
        <>
          <Space size="middle" direction="horizontal">
            <Tooltip title="View">
              <Button
                type="primary"
                ghost
                size="large"
                shape="circle"
                htmlType="button"
                onClick={e => {
                  e.stopPropagation();
                  this.viewModalHandler(record._id);
                }}
              >
                <EyeFilled />
              </Button>
            </Tooltip>
            {(this.isRoleHR() || this.isRoleBU()) && (
              <Tooltip title="Edit">
                <Button
                  type="primary"
                  ghost
                  size="large"
                  shape="circle"
                  htmlType="button"
                  onClick={e => {
                    e.stopPropagation();
                    this.editModalHandler(record._id);
                  }}
                >
                  <EditOutlined />
                </Button>
              </Tooltip>
            )}
          </Space>
        </>
      ),
    },
  ];

  getColumnProps = () =>
    this.isRoleHR() || this.isRoleBU()
      ? [...this.tableColumns]
      : this.tableColumns;

  reset = () => {
    const { dispatch, updateField } = this.props;
    [
      'email',
      'firstName',
      'lastName',
      'menteeId',
      'skills',
      'mentorId',
    ].forEach(key => {
      dispatch(change(MENTEE_LIST_FORM_KEY, key, ''));
      updateField(key, '');
    });
  };

  /**
   * Toggle Modal
   */
  toggleModals = () => {
    this.reset();
    const { showMenteeFormModal, menteeId } = this.state;
    this.setState({
      showMenteeFormModal: !showMenteeFormModal,
      menteeId: showMenteeFormModal ? '' : menteeId,
    });
  };

  /**
   *
   * @param {*} menteeId
   */
  editModalHandler = menteeId => {
    // Edit
    const { menteeList } = this.state;
    const { updateField, dispatch } = this.props;
    const mentee = menteeList.find(item => item._id === menteeId);
    if (mentee) {
      const storeData = {
        menteeId: `${mentee.menteeId}`,
        skills: mentee.skills,
        firstName: mentee.firstName,
        lastName: mentee.lastName,
        email: mentee.email,
        mentorId: mentee.mentorId || '',
      };
      Object.keys(storeData).forEach(key => {
        dispatch(change(MENTEE_LIST_FORM_KEY, key, storeData[key]));
        updateField(key, storeData[key]);
      });

      this.setState({
        showMenteeFormModal: true,
        menteeId,
      });
    }
  };

  /**
   * Table sort, pagination change
   * @param {*} pagination
   * @param {*} _filters
   * @param {*} sorter
   */
  onTableOptionChange = (pagination, _filters, sorter) => {
    // Handle empty sorter or multiple sorters
    const { sortBy: stateSortBy, sortType: stateSortType } = this.state;
    let sortBy = stateSortBy;
    let sortType = stateSortType;

    if (sorter && sorter.field) {
      sortBy = sorter.field;
      sortType = GET_SORT_ORDER(sorter.order, SORTING.ASC);
    }

    setDeepLinkURL({
      status: this.state.activeFilter,
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
      searchText: this.state.searchValue,
    });

    this.getMenteeList({
      pagination,
      sortType,
      sortBy,
    });
  };

  isRoleHR = () => getUserData().role === ROLES.HR;

  isRoleBU = () => getUserData().role === ROLES.BU;

  isRoleUser = () => getUserData().role === ROLES.USER;

  delayedSearchResults = debounce(searchValue => {
    this.getMenteeList({
      searchValue,
      filterApplied: true,
      pagination: {
        ...this.state.pagination,
        current: DEFAULT_PAGE, // Reset to first page on search
      },
    });

    setDeepLinkURL({
      status: this.state.activeFilter,
      currentPage: DEFAULT_PAGE,
      pageSize: this.state.pagination.pageSize,
      searchText: searchValue,
    });
  }, 500); // Reduced to 500ms for quicker feedback

  handleSearchMentee = e => {
    const searchVal = e.target.value;

    this.setState(
      prevState => ({
        searchValue: searchVal,
        pagination: {
          ...prevState.pagination,
          current: DEFAULT_PAGE,
        },
      }),
      () => {
        this.delayedSearchResults(searchVal);
      },
    );
  };

  getActiveInactive = value => {
    this.setState(prevState => ({
      activeFilter: value,
      pagination: {
        ...prevState.pagination,
        current: DEFAULT_PAGE,
      },
    }));

    setDeepLinkURL({
      status: value,
      currentPage: DEFAULT_PAGE,
      pageSize: this.state.pagination.pageSize,
      searchText: this.state.searchValue,
    });

    this.getMenteeList({
      isActive: value,
      filterApplied: true,
      pagination: {
        ...this.state.pagination,
        current: DEFAULT_PAGE,
      },
    });
  };

  setPaginationCount = value => {
    const newPagination = {
      current: DEFAULT_PAGE,
      pageSize: parseInt(value, 10),
    };

    this.setState(prevState => ({
      pagination: {
        ...prevState.pagination,
        ...newPagination,
      },
    }));

    setDeepLinkURL({
      status: this.state.activeFilter,
      currentPage: DEFAULT_PAGE,
      pageSize: value,
      searchText: this.state.searchValue,
    });

    this.getMenteeList({
      pagination: newPagination,
      filterApplied: true,
    });
  };

  viewModalHandler = menteeId => {
    const { menteeList } = this.state;
    const mentee = menteeList.find(item => item._id === menteeId);
    this.setState({
      showViewModal: true,
      viewMentee: mentee,
    });
  };

  closeViewModal = () => {
    this.setState({
      showViewModal: false,
      viewMentee: null,
    });
  };

  toggleDownloadModal = () => {
    this.setState(prevState => ({
      showDownloadModal: !prevState.showDownloadModal,
    }));
  };

  handleDownloadAllMentees = async () => {
    this.setState({ downloadLoading: true });
    try {
      // Adjust the endpoint to fetch ALL mentees (no pagination)
      const response = await request(`${API_ENDPOINTS.GET_MENTEE}`, {
        method: 'GET',
      });
      const mentees = response.data.docs || [];

      // Prepare CSV
      const csvRows = [
        ['Employee Id', 'Name', 'Email', 'Label'], // header
        ...mentees.map(m => [
          m.employeeId,
          `${m.firstName} ${m.lastName}`,
          m.email,
          Array.isArray(m.label) ? m.label.join(', ') : m.label,
        ]),
      ];
      const csvContent = csvRows
        .map(row =>
          row
            .map(String)
            .map(v => `"${v.replace(/"/g, '""')}"`)
            .join(','),
        )
        .join('\n');

      // Download
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'mentees.csv';
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);

      this.setState({ downloadLoading: false, showDownloadModal: false });
    } catch (error) {
      notification.error({
        message: 'Download failed',
        description: error.message,
      });
      this.setState({ downloadLoading: false });
    }
  };

  handleTabChange = tab => {
    this.setState({ activeTab: tab });
  };

  handleKeyPress = (event, tab) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.handleTabChange(tab);
    }
  };

  render() {
    const {
      isPaginationLoading,
      menteeList,
      pagination,
      showMenteeFormModal,
      activeFilter,
      searchValue,
      activeTab,
    } = this.state;
    const isMenteesTab = activeTab === 'mentees';
    const isPliTab = activeTab === 'pli';
    const isLoading = isPaginationLoading;

    return (
      <div>
        <PageHeader
          title={
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span
                key="mentees-tab"
                role="tab"
                tabIndex={0}
                aria-selected={isMenteesTab}
                style={{
                  fontWeight: 600,
                  color: isMenteesTab ? '#7B1FA2' : '#222',
                  borderBottom: isMenteesTab ? '2px solid #7B1FA2' : 'none',
                  paddingBottom: 4,
                  marginRight: 32,
                  fontSize: 18,
                  cursor: 'pointer',
                }}
                onClick={() => this.handleTabChange('mentees')}
                onKeyPress={e => this.handleKeyPress(e, 'mentees')}
              >
                <FormattedMessage {...messages.myMentees} />
              </span>
              <span
                key="pli-tab"
                role="tab"
                tabIndex={0}
                aria-selected={isPliTab}
                style={{
                  fontWeight: 600,
                  color: isPliTab ? '#7B1FA2' : '#222',
                  borderBottom: isPliTab ? '2px solid #7B1FA2' : 'none',
                  paddingBottom: 4,
                  fontSize: 18,
                  cursor: 'pointer',
                }}
                onClick={() => this.handleTabChange('pli')}
                onKeyPress={e => this.handleKeyPress(e, 'pli')}
              >
                <FormattedMessage {...messages.pliRating} />
              </span>
            </div>
          }
          className="site-page-header"
          extra={[
            isMenteesTab && (
              <React.Fragment key="mentees-extra">
                <div className="u-mr-3 u-d-inline" key="filter">
                  <Select
                    value={activeFilter}
                    style={{ width: 100 }}
                    onChange={this.getActiveInactive}
                  >
                    <Option
                      value="1"
                      selected={activeFilter === 1 ? 'selected' : ''}
                    >
                      Active
                    </Option>
                    <Option
                      value="0"
                      selected={activeFilter === 0 ? 'selected' : ''}
                    >
                      Inactive
                    </Option>
                  </Select>
                </div>
                <Select
                  key="pageSize"
                  value={pagination.pageSize}
                  style={{ width: 100 }}
                  onChange={this.setPaginationCount}
                >
                  <Option value="10">10</Option>
                  <Option value="20">20</Option>
                  <Option value="50">50</Option>
                  <Option value="100">100</Option>
                </Select>
                <Input
                  key="search"
                  prefix={<SearchOutlined />}
                  placeholder="Search For Mentee"
                  allowClear
                  value={searchValue}
                  style={{ width: 'inherit' }}
                  onChange={this.handleSearchMentee}
                />
                {!!this.isRoleHR() && (
                  <Button
                    data-testid="DOWNLOAD_MODAL"
                    key="download"
                    type="primary"
                    onClick={this.toggleDownloadModal}
                    title="Download"
                    loading={this.state.downloadLoading}
                  >
                    Download
                  </Button>
                )}
              </React.Fragment>
            ),
          ]}
        >
          {isMenteesTab ? (
            <StyledTableContainer>
              <Table
                className="mentee-table"
                {...menteeTableProps}
                rowKey={record => record._id}
                pagination={{
                  ...pagination,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  pageSizeOptions: ['10', '20', '50', '100'],
                  showTotal: (total, range) =>
                    `${range[0]}-${range[1]} of ${total} items`,
                }}
                loading={isLoading}
                columns={this.getColumnProps()}
                dataSource={menteeList}
                onChange={this.onTableOptionChange}
                scroll={{ x: 1200 }}
                onRow={record => ({
                  onClick: () => {
                    if (record && record.employeeId) {
                      this.props.history.replace('/mentee');
                      this.props.history.push(
                        `/pli/employee-profile/${record.employeeId}`,
                      );
                    }
                  },
                })}
                locale={{
                  emptyText: !isLoading && (
                    <div
                      key="no-mentees"
                      style={{
                        padding: '20px',
                        textAlign: 'center',
                        fontSize: '16px',
                        color: '#666',
                      }}
                    >
                      <FormattedMessage {...messages.noMenteesFound} />
                    </div>
                  ),
                }}
              />
            </StyledTableContainer>
          ) : (
            <div
              key="pli-content"
              style={{ padding: '20px', fontSize: '16px' }}
            >
              <FormattedMessage {...messages.pliRating} />
            </div>
          )}
        </PageHeader>
        {showMenteeFormModal && (
          <EditMentee
            stateValues={this.state}
            updateState={updatedValues => {
              this.setState(updatedValues);
            }}
            toggleModal={() => this.toggleModals()}
            refreshMenteeList={() => this.getMenteeList()}
          />
        )}
        {this.state.showViewModal && (
          <Modal
            title="Mentee Details"
            open={this.state.showViewModal}
            onCancel={this.closeViewModal}
            footer={[
              <Button key="close" onClick={this.closeViewModal}>
                Close
              </Button>,
            ]}
          >
            <p>
              <b>First Name:</b> {this.state.viewMentee.firstName}
            </p>
            <p>
              <b>Last Name:</b> {this.state.viewMentee.lastName}
            </p>
            <p>
              <b>Employee ID:</b> {this.state.viewMentee.employeeId}
            </p>
            <p>
              <b>Label:</b>{' '}
              {Array.isArray(this.state.viewMentee.label)
                ? this.state.viewMentee.label.join(', ')
                : this.state.viewMentee.label}
            </p>
            <p>
              <b>Designation:</b> {this.state.viewMentee.designation}
            </p>
          </Modal>
        )}
        {this.state.showDownloadModal && (
          <Modal
            title="Download All Mentees"
            open={this.state.showDownloadModal}
            onCancel={this.toggleDownloadModal}
            footer={[
              <Button key="cancel" onClick={this.toggleDownloadModal}>
                Cancel
              </Button>,
              <Button
                key="download"
                type="primary"
                loading={this.state.downloadLoading}
                onClick={this.handleDownloadAllMentees}
              >
                Download
              </Button>,
            ]}
          >
            <p>
              This will download all mentee names and information as a CSV file.
            </p>
          </Modal>
        )}
      </div>
    );
  }
}

MenteeListing.propTypes = {
  menteeId: PropTypes.string,
  // Redux-form
  dispatch: PropTypes.func.isRequired,
  reset: PropTypes.func.isRequired,
  pristine: PropTypes.bool,
  submitting: PropTypes.bool,
  invalid: PropTypes.bool,
  formState: PropTypes.object,
  // Action
  updateField: PropTypes.func.isRequired,
  // Store
  menteeStoreData: PropTypes.object,
  // Router
  history: PropTypes.object,
};

const withReducer = useInjectReducer({
  key: MENTEE_LIST_FORM_KEY,
  reducer,
});

const mapStateToProps = state => {
  const getStateValues = createStructuredSelector({
    menteeStoreData: makeSelectMenteeList(),
  });
  return {
    formState: state,
    ...getStateValues(state),
  };
};

export const mapDispatchToProps = dispatch => ({
  updateField: (key, value) => dispatch(actions.updateField(key, value)),
});

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

export default compose(
  withRouter,
  withReducer,
  withConnect,
  reduxForm({
    form: MENTEE_LIST_FORM_KEY,
    fields: ['email', 'EmployeeId', 'Name', 'label'],
    validate: formValidations.createValidator({
      menteeId: [formValidations.required, formValidations.isNumber],
      firstName: [formValidations.required],
      lastName: [formValidations.required],
      email: [formValidations.required, formValidations.validEmail],
    }),
  }),
)(MenteeListing);
