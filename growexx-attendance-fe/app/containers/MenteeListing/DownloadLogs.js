/* eslint-disable jsx-a11y/label-has-associated-control */
import React, { PureComponent } from 'react';
import { withRouter } from 'react-router-dom';
import { compose } from 'redux';
import { Field, reduxForm } from 'redux-form';
import { Modal, Form, notification, Button } from 'antd';
import { FormattedMessage } from 'react-intl';
import PropTypes from 'prop-types';
import { ARangePicker } from 'utils/Fields';
import { get } from 'lodash';
import request from 'utils/request';
import useInjectReducer from 'utils/injectReducer';
import AsyncSelect from 'react-select/async';

import reducer from './reducer';

import messages from './messages';
import { USER_LIST_FORM_KEY, DropDownStyle } from './constants';
import { DEFAULT_LIMIT, API_ENDPOINTS, DEFAULT_PAGE } from '../constants';

const DOWNLOAD_FORM_OPTIONS = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};
class DownloadLogs extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      showDownloadModal: false,
      downloadLoading: false,
    };
  }

  onLoadUser = (search, callback) => {
    // eslint-disable-next-line no-new
    new Promise(resolve => {
      const data = { method: 'GET' };
      const requestURL = `${
        API_ENDPOINTS.USERS
      }?pageSize=${DEFAULT_LIMIT}&skip=${DEFAULT_PAGE}&name=${search}`;
      request(requestURL, data).then(response => {
        const userList = get(response, 'data.docs', []).map(user => ({
          // eslint-disable-next-line no-underscore-dangle
          value: user._id,
          label: `${user.firstName} ${user.lastName}`,
        }));
        callback(userList);
        resolve(userList);
      });
    });
  };

  downloadAllMentees = () => {
    const { updateState } = this.props;
    updateState({ downloadLoading: true });

    // Use the correct API endpoint for all mentees
    const URL = API_ENDPOINTS.ALL_MENTEES_DOWNLOAD; // Define this in your constants

    request(URL, {
      method: 'GET',
      blob: true,
    })
      .then(res => {
        const url = URL.createObjectURL(res);
        const a = document.createElement('a');
        a.href = url;
        a.style.display = 'none';
        a.download = `all-mentees.csv`;
        a.target = '_blank';
        document.body.append(a);
        a.click();
        URL.revokeObjectURL(url);
        document.body.removeChild(a);
        notification.success({
          message: 'Downloaded successfully',
        });
        updateState({
          showDownloadModal: false,
          downloadLoading: false,
        });
      })
      .catch(error => {
        notification.error({
          message: 'Failed to download',
          description: error.message,
        });
        updateState({
          downloadLoading: false,
        });
      });
  };

  toggleDownloadModal = () => {
    this.setState(prevState => ({
      showDownloadModal: !prevState.showDownloadModal,
    }));
  };

  handleDownloadAllMentees = async () => {
    this.setState({ downloadLoading: true });
    try {
      // Adjust the endpoint to fetch ALL mentees (no pagination)
      const response = await request(`${API_ENDPOINTS.USER_LIST}?all=true`, {
        method: 'GET',
      });
      const mentees = response.data.docs || [];

      // Prepare CSV
      const csvRows = [
        ['Employee Id', 'Name', 'Email', 'Label'], // header
        ...mentees.map(m => [
          m.employeeId,
          `${m.firstName} ${m.lastName}`,
          m.email,
          Array.isArray(m.label) ? m.label.join(', ') : m.label,
        ]),
      ];
      const csvContent = csvRows
        .map(row =>
          row
            .map(String)
            .map(v => `"${v.replace(/"/g, '""')}"`)
            .join(','),
        )
        .join('\n');

      // Download
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'mentees.csv';
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);

      this.setState({ downloadLoading: false, showDownloadModal: false });
    } catch (error) {
      notification.error({
        message: 'Download failed',
        description: error.message,
      });
      this.setState({ downloadLoading: false });
    }
  };

  render() {
    const layout = {
      labelCol: {
        span: 7,
        xs: 24,
        sm: 7,
      },
      wrapperCol: {
        span: 14,
        xs: 24,
        sm: 14,
      },
    };
    const {
      stateValues: { isLoading, showDownloadModal, logRange, downloadLoading },
      updateState,
      toggleModal,
    } = this.props;

    const TEST_IDS = '';
    return (
      <Modal
        title="Download logs"
        visible={showDownloadModal}
        onOk={this.downloadLogs}
        confirmLoading={isLoading}
        onCancel={toggleModal}
        okButtonProps={{
          disabled: !logRange,
          loading: downloadLoading,
          'data-testid': TEST_IDS.DOWNLOAD_BTN,
        }}
        okText={<FormattedMessage {...messages.downloadBtn} />}
        cancelButtonProps={{
          'data-testid': TEST_IDS.DOWNLOAD_LOGS_CANCEL_BTN,
        }}
      >
        <Button
          type="primary"
          onClick={this.downloadAllMentees}
          loading={this.props.stateValues.downloadLoading}
          style={{ marginBottom: 16 }}
        >
          Download All Mentees
        </Button>
        <Form
          onSubmit={this.downloadLogs}
          {...DOWNLOAD_FORM_OPTIONS}
          {...layout}
        >
          <Field
            label="Period"
            name="period"
            component={ARangePicker}
            placeholder={['From', 'To']}
            hasFeedback
            required
            value={logRange}
            onFocus={e => e.preventDefault()}
            onBlur={e => e.preventDefault()}
            onChange={e => updateState({ logRange: e })}
          />
          <Form.Item label="Employee" required>
            <AsyncSelect
              instanceId="employee"
              className="employee-container"
              classNamePrefix="employee-select"
              defaultOptions
              isSearchable
              isClearable
              styles={DropDownStyle}
              placeholder="Search Employee"
              loadOptions={this.onLoadUser}
              onChange={this.onEmployeeChange}
            />
          </Form.Item>
        </Form>
        {this.state.showDownloadModal && (
          <Modal
            title="Download All Mentees"
            visible={this.state.showDownloadModal}
            onCancel={this.toggleDownloadModal}
            footer={[
              <Button key="cancel" onClick={this.toggleDownloadModal}>
                Cancel
              </Button>,
              <Button
                key="download"
                type="primary"
                loading={this.state.downloadLoading}
                onClick={this.handleDownloadAllMentees}
              >
                Download
              </Button>,
            ]}
          >
            <p>
              This will download all mentee names and information as a CSV file.
            </p>
          </Modal>
        )}
      </Modal>
    );
  }
}

DownloadLogs.propTypes = {
  // props from parent
  stateValues: PropTypes.object,
  updateState: PropTypes.func,
  toggleModal: PropTypes.func,
};

const withReducer = useInjectReducer({
  key: USER_LIST_FORM_KEY,
  reducer,
});

export default compose(
  withRouter,
  withReducer,
  reduxForm({
    form: USER_LIST_FORM_KEY,
    fields: ['period'],
  }),
)(DownloadLogs);
