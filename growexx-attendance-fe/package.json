{"name": "react-boilerplate", "version": "5.0.0", "description": "A highly scalable, offline-first foundation with the best DX and a focus on performance and best practices", "repository": {"type": "git", "url": "https://github.com/growexx/react-user-boilerplate.git"}, "engines": {"npm": ">=5", "node": ">=14", "react": ">=16.8", "mongo": "=4.4"}, "author": "growexx", "license": "MIT", "scripts": {"analyze:clean": "rimraf stats.json", "preanalyze": "npm run analyze:clean", "analyze": "node ./internals/scripts/analyze.js", "extract-intl": "node ./internals/scripts/extract-intl.js", "npmcheckversion": "node ./internals/scripts/npmcheckversion.js", "preinstall": "npm run npmcheckversion", "prebuild": "npm run build:clean", "build": "webpack --config internals/webpack/webpack.prod.babel.js --color -p --progress --hide-modules --display-optimization-bailout", "build:clean": "rimraf ./build", "start": "node server", "start:tunnel": "node server", "start:production": "npm run test && npm run build && npm run start:prod", "start:prod": "node server", "presetup": "npm i chalk shelljs", "setup": "node ./internals/scripts/setup.js", "clean": "shjs ./internals/scripts/clean.js", "clean:all": "npm run analyze:clean && npm run test:clean && npm run build:clean", "generate": "plop --plopfile internals/generators/index.js", "lint": "npm run lint:js && npm run lint:css", "lint:css": "stylelint app/**/*.js", "lint:eslint": "eslint --ignore-path .gitignore --ignore-pattern internals/scripts", "lint:eslint:fix": "eslint --ignore-path .gitignore --ignore-pattern internals/scripts --fix", "lint:js": "npm run lint:eslint -- . ", "lint:staged": "lint-staged", "pretest": "npm run test:clean && npm run lint", "test:clean": "rimraf ./coverage", "test": "jest --maxWorkers=2 --coverage --updateSnapshot; exit 0", "test:snapshot": "jest --coverage --updateSnapshot --watchAll", "test:watch": "jest --watchAll", "coveralls": "cat ./coverage/lcov.info | coveralls", "prettify": "prettier --write", "storybook": "start-storybook -p 6006", "build-storybook": "build-storybook"}, "browserslist": ["last 2 versions", "> 1%", "IE 10"], "babel": {"plugins": ["@babel/plugin-proposal-optional-chaining"]}, "lint-staged": {"*.js": ["npm run lint:eslint:fix", "git add --force"], "*.json": ["prettier --write", "git add --force"]}, "pre-commit": "lint:staged", "resolutions": {"babel-core": "7.0.0-bridge.0"}, "dependencies": {"@babel/polyfill": "7.4.3", "@fortawesome/fontawesome-svg-core": "^1.2.35", "@fortawesome/free-brands-svg-icons": "^5.15.3", "@fortawesome/free-regular-svg-icons": "^5.15.3", "@fortawesome/free-solid-svg-icons": "^5.15.3", "@fortawesome/react-fontawesome": "^0.1.14", "ag-grid-community": "^31.3.4", "ag-grid-enterprise": "^31.3.4", "ag-grid-react": "^31.3.4", "antd": "^4.14.0", "babel-plugin-styless": "^1.4.25", "base-64": "^1.0.0", "chalk": "2.4.2", "compression": "1.7.4", "connected-react-router": "6.8.0", "dotenv-webpack": "^7.0.2", "eventemitter3": "^4.0.7", "express": "4.16.4", "file-saver": "^2.0.5", "fontfaceobserver": "2.1.0", "history": "4.9.0", "hoist-non-react-statics": "3.3.0", "immer": "9.0.6", "immutable": "^4.3.7", "intl": "1.2.5", "invariant": "2.2.4", "ip": "1.1.5", "less-vars-to-js": "^1.3.0", "lodash": "^4.17.21", "markdown-to-jsx": "^7.3.2", "minimist": "^1.2.6", "moment": "^2.29.1", "moment-timezone": "^0.6.0", "numeral": "^2.0.6", "prop-types": "15.7.2", "react": "^16.14.0", "react-dom": "^16.14.0", "react-helmet": "6.0.0-beta", "react-html-parser": "^2.0.2", "react-infinite-scroller": "^1.2.4", "react-intl": "2.8.0", "react-loading-skeleton": "^3.5.0", "react-otp-input": "^2.3.0", "react-redux": "7.1.0", "react-router-dom": "^5.2.0", "react-select": "^4.3.1", "redux": "4.0.1", "redux-form": "^8.3.7", "redux-injectors": "^2.1.0", "redux-mock-store": "^1.5.4", "redux-saga": "1.0.2", "reselect": "4.0.0", "sanitize.css": "8.0.0", "sha256": "^0.2.0", "sonar-scanner": "^3.1.0", "styled-components": "4.4.0", "validator": "^13.5.2", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/cli": "^7.12.13", "@babel/core": "^7.12.13", "@babel/plugin-proposal-class-properties": "7.4.0", "@babel/plugin-syntax-dynamic-import": "7.2.0", "@babel/plugin-transform-modules-commonjs": "7.4.3", "@babel/plugin-transform-react-constant-elements": "7.2.0", "@babel/plugin-transform-react-inline-elements": "7.2.0", "@babel/preset-env": "7.4.3", "@babel/preset-react": "7.0.0", "@babel/register": "7.4.0", "@storybook/addon-actions": "^6.2.8", "@storybook/addon-essentials": "^6.2.8", "@storybook/addon-links": "^6.2.8", "@storybook/react": "^6.2.8", "@testing-library/dom": "^7.30.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^12.1.5", "@testing-library/user-event": "^13.1.1", "@types/jest": "^26.0.22", "add-asset-html-webpack-plugin": "3.1.3", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-loader": "8.0.5", "babel-plugin-dynamic-import-node": "2.2.0", "babel-plugin-import": "^1.13.3", "babel-plugin-lodash": "3.3.4", "babel-plugin-react-intl": "3.0.1", "babel-plugin-styled-components": "1.10.0", "babel-plugin-transform-react-remove-prop-types": "0.4.24", "circular-dependency-plugin": "5.0.2", "compare-versions": "3.4.0", "compression-webpack-plugin": "2.0.0", "core-js": "^3.9.1", "coveralls": "3.0.3", "css-loader": "2.1.1", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-airbnb-base": "13.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-webpack": "0.11.1", "eslint-plugin-import": "2.17.2", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-prettier": "3.0.1", "eslint-plugin-react": "7.12.4", "eslint-plugin-react-hooks": "1.6.0", "eslint-plugin-redux-saga": "1.0.0", "file-loader": "3.0.1", "html-loader": "0.5.5", "html-webpack-plugin": "3.2.0", "image-webpack-loader": "4.6.0", "imports-loader": "0.8.0", "jest-cli": "24.7.1", "jest-dom": "3.1.3", "jest-environment-jsdom-fourteen": "^1.0.1", "jest-styled-components": "^6.3.1", "less": "^4.1.1", "less-loader": "^7.3.0", "lint-staged": "8.1.5", "ngrok": "3.1.1", "node-plop": "0.18.0", "null-loader": "0.1.1", "offline-plugin": "5.0.6", "plop": "2.3.0", "pre-commit": "1.2.2", "prettier": "1.17.0", "react-app-polyfill": "0.2.2", "react-test-renderer": "16.8.6", "react-testing-library": "6.1.2", "rimraf": "2.6.3", "shelljs": "0.8.5", "storybook-addon-react-docgen": "^1.2.42", "style-loader": "0.23.1", "stylelint": "10.0.1", "stylelint-config-recommended": "2.2.0", "stylelint-config-styled-components": "0.1.1", "stylelint-processor-styled-components": "1.6.0", "svg-url-loader": "2.3.2", "terser": "^3.14.1", "terser-webpack-plugin": "^4.2.3", "url-loader": "1.1.2", "webpack": "^4.28.4", "webpack-cli": "3.3.0", "webpack-dev-middleware": "3.6.2", "webpack-hot-middleware": "2.24.3", "webpack-pwa-manifest": "^4.3.0", "whatwg-fetch": "3.0.0"}, "proxy": "http://localhost:3001"}