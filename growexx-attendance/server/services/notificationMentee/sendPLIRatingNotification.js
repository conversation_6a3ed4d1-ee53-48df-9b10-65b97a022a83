const fs = require('fs');
const path = require('path');
const logger = require('../../util/logger');
const EmailService = require('../../util/sendEmail');
const User = require('../../models/user.model');
const JWT = require('../../util/jwt');

/**
 * Send PLI notification email to user
 * @param {string} userId - Mentee ID
 * @param {object} payload - Data including month, year, score, mentorName, projectName
 * @returns {Promise<boolean>}
 */
const sendPliNotificationEmail = async (userId, payload) => {
    try {
        const {
            month,
            year,
            score,
            mentorName,
            projectName,
            companyName = 'Growexx', // fallback default
            pliRatingId
        } = payload;

        const userDetails = await User.findById(userId);
        if (!userDetails) {
            throw new Error('User not found');
        }
        const superadminEmail = '<EMAIL>';

        const { email, firstName, lastName, employeeId } = userDetails;
        const menteeName = `${firstName} ${lastName}`;

        const redirectPath = pliRatingId
            ? `/my-pli-rating?pliRatingId=${pliRatingId}&view=mentee-view`
            : '/pli';

        const token = JWT.createTempAuthToken(userId, {
            redirectTo: redirectPath
        });

        const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
        const pliViewUrl =
      `${baseUrl}/pli/employee-profile-redirect?pliRatingId=${pliRatingId}` +
      `&token=${token}&view=mentee-view&menteeId=${employeeId}`;

        const templatePath = path.join(
            process.cwd(),
            'emailTemplates',
            'menteeNotificationPLI.html'
        );

        const templateVariables = {
            MENTEE_NAME: menteeName,
            MONTH: month,
            YYYY: year,
            X: score,
            MENTOR_NAME: mentorName,
            PROJECT_NAME: projectName,
            ACTIONURL: pliViewUrl,
            APPURL: baseUrl,
            COMPANY_NAME: companyName
        };

        await EmailService.prepareAndSendEmail(
            [email, superadminEmail],
            `Your PLI Score for ${month} ${year} is Updated – Action Required`,
            templatePath,
            templateVariables
        );

        logger.info(`PLI notification email sent to user: ${email}`);
        return true;
    } catch (error) {
        logger.error(`Error sending PLI notification email: ${error.message}`);
        return false;
    }
};

module.exports = sendPliNotificationEmail;
