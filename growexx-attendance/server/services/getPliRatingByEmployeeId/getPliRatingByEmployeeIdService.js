/**
 * @name getPliRatingByEmployeeIdService
 * <AUTHOR>
 */
const PLIRating = require("../../models/pliRating.model");
const User = require("../../models/user.model");
const Project = require("../../models/project.model");
const PLIParameters = require("../../models/pliParameters.model");
const GeneralError = require("../../util/GeneralError");
const GetPliRatingByIdService = require("../getPliRatingById/getPliRatingByIdService");
// Locale will be passed as a parameter

class GetPliRatingByEmployeeIdService {
  constructor() {
    this.getPliRatingByEmployeeId = this.getPliRatingByEmployeeId.bind(this);
    this.validateGetPliRatingByEmployeeId =
      this.validateGetPliRatingByEmployeeId.bind(this);
  }

  /**
   * @desc This function is used to validate the input
   * @param {Number} employeeId employeeId
   * @param {String} key key
   * @param {Object} locale Localization object
   */
  validateGetPliRatingByEmployeeId(employeeId, key, locale) {
    if (!employeeId) {
      throw new GeneralError(locale.FIELD_REQUIRED.replace("{0}", key), 400);
    }
  }

  /**
   * @desc Get user details and format them for response
   * @param {Object} userId User ID
   * @return {Object} Formatted user data
   */
  async getUserDetails(userId) {
    const user = await User.findById(userId);
    if (!user) {
      return null;
    }

    const userName = `${user.firstName} ${user.lastName}`.trim();
    return {
      _id: userId, // Include the original ObjectId
      id: userId, // Include as id for frontend compatibility
      name: userName,
      email: user.email,
      employeeId: user.employeeId,
    };
  }

  /**
   * @desc Get project details and format them for response
   * @param {Object} projectId Project ID
   * @return {Object} Formatted project data
   */
  async getProjectDetails(projectId) {
    const project = await Project.findById(projectId);
    if (!project) {
      return null;
    }

    // Special case handling for project names
    let projectName = project.projectName || project.name;

    // Map specific project names (e.g., "PSI II" to "COE")
    if (projectName === "PSI II") {
      projectName = "COE";
    }

    return {
      _id: projectId, // Include the original ObjectId
      id: projectId, // Include as id for frontend compatibility
      name: projectName,
      code: project.projectCode,
      client: project.clientName,
    };
  }

  /**
   * @desc Create parameter object from parameter score
   * @param {Object} parameterScore Parameter score object
   * @return {Object} Parameter object
   */
  createParameterObject(parameterScore) {
    const paramObj = {
      name: parameterScore.parentParameter,
      type: parameterScore.projectType,
    };

    return paramObj;
  }

  /**
   * @desc Calculate the average score from sprint scores
   * @param {Array} sprintScores Array of sprint scores
   * @return {Number} Calculated average
   */
  calculateAverageScore(sprintScores) {
    if (!sprintScores || !sprintScores.length) {
      return 0;
    }

    const scores = sprintScores
      .map((sprint) => Number(sprint.score))
      .filter((score) => !isNaN(score));

    if (!scores.length) {
      return 0;
    }

    const totalScore = scores.reduce((sum, score) => sum + score, 0);
    return Number((totalScore / scores.length).toFixed(2));
  }

  /**
   * @desc Process child scores for a parameter
   * @param {Array} childScores Array of child scores
   * @param {Object} parameterInfo Parameter information
   * @param {String} userDesignation User designation
   * @return {Array} Processed child scores
   */
  async processChildScores(childScores, parameterInfo, userDesignation) {
    const processedScores = [];

    for (const childScore of childScores) {
      const processedScore = { ...childScore };

      // Store the original child parameter ID
      const originalChildParameterId = childScore.childParameterId;

      processedScore.childParameter = childScore.childParameterId;
      processedScore._id = originalChildParameterId; // Keep the original ID

      // Don't delete childParameterId anymore
      processedScore.childParameterId = originalChildParameterId;

      try {
        // 1. Get child parameter weightage
        processedScore.childParameterWeightage =
          await GetPliRatingByIdService.getChildParameterWeighatge(
            userDesignation,
            parameterInfo.type,
            parameterInfo.name,
            processedScore.childParameter
          );

        // 2. Calculate average of sprint scores
        processedScore.calculation = this.calculateAverageScore(
          childScore.sprintScores
        );

        // 3. Calculate weightage average
        processedScore.weightageAverage = Number(
          (
            processedScore.calculation * processedScore.childParameterWeightage
          ).toFixed(2)
        );
      } catch (error) {
        // Set default values if there's an error
        processedScore.childParameterWeightage = 0;
        processedScore.calculation = 0;
        processedScore.weightageAverage = 0;
      }

      processedScores.push(processedScore);
    }

    return processedScores;
  }

  /**
   * @desc Process a single parameter score
   * @param {Object} processedScore Parameter score to process
   * @param {String} userDesignation User designation
   * @return {Object} Processed parameter score
   */
  async processSingleParameterScore(processedScore, userDesignation) {
    if (!processedScore.parameterId) {
      return processedScore;
    }

    try {
      // Store the original parameter ID
      const originalParameterId = processedScore.parameterId;

      // Create parameter object
      processedScore.parameter = this.createParameterObject(processedScore);

      // Add the original parameter ID to the parameter object
      processedScore.parameter._id = originalParameterId;
      processedScore.parameter.id = originalParameterId;

      // Process child scores if PLI parameters exist
      await this.processChildScoresIfNeeded(processedScore, userDesignation);

      // Remove extracted fields but keep parameterId
      this.removeExtractedFields(processedScore);

      // Add back the parameter ID
      processedScore._id = originalParameterId;
      processedScore.parameterId = originalParameterId;
    } catch (error) {
      // Handle errors and still create parameter object
      processedScore.parameter = this.createParameterObject(processedScore);
      this.removeExtractedFields(processedScore);
    }

    return processedScore;
  }

  /**
   * @desc Remove extracted fields from processed score
   * @param {Object} processedScore Parameter score
   */
  removeExtractedFields(processedScore) {
    // Don't delete parameterId anymore
    delete processedScore.projectType;
    delete processedScore.parentParameter;
  }

  /**
   * @desc Process child scores if needed
   * @param {Object} processedScore Parameter score
   * @param {String} userDesignation User designation
   */
  async processChildScoresIfNeeded(processedScore, userDesignation) {
    if (!processedScore.childScores) {
      return;
    }

    const pliParametersDoc = await PLIParameters.findOne({ isActive: 1 });
    if (pliParametersDoc) {
      processedScore.childScores = await this.processChildScores(
        processedScore.childScores,
        processedScore.parameter,
        userDesignation
      );
    }
  }

  /**
   * @desc Process parameter scores for a PLI rating
   * @param {Array} parameterScores Array of parameter scores
   * @param {String} userDesignation User designation
   * @return {Array} Processed parameter scores
   */
  async processParameterScores(parameterScores, userDesignation) {
    const processedScores = [];

    for (const parameterScore of parameterScores) {
      const processedScore = { ...parameterScore };
      const processedItem = await this.processSingleParameterScore(
        processedScore,
        userDesignation
      );
      processedScores.push(processedItem);
    }

    return processedScores;
  }

  /**
   * @desc Process project ratings for a PLI rating
   * @param {Array} projectRatings Array of project ratings
   * @param {String} userDesignation User designation
   * @return {Array} Processed project ratings
   */
  async processProjectRatings(projectRatings, userDesignation) {
    const processedRatings = [];

    for (const projectRating of projectRatings) {
      const processedRating = { ...projectRating };

      // Store the original project ID
      const originalProjectId = processedRating.projectId;

      // Add project details while preserving the original ID
      if (processedRating.projectId) {
        const projectData = await this.getProjectDetails(
          processedRating.projectId
        );
        if (projectData) {
          processedRating.project = projectData;
          // Keep the original projectId
          processedRating.projectId = originalProjectId;
        }
      }

      // Process parameter scores
      if (processedRating.parameterScores) {
        processedRating.parameterScores = await this.processParameterScores(
          processedRating.parameterScores,
          userDesignation
        );
      }

      processedRatings.push(processedRating);
    }

    return processedRatings;
  }

  /**
   * @desc Find employee by employee ID
   * @param {Number} employeeId Employee ID
   * @param {Object} locale Localization object
   * @return {Object} Employee object
   */
  async findEmployeeById(employeeId, locale) {
    const employee = await User.findOne({
      employeeId: parseInt(employeeId, 10),
    });
    if (!employee) {
      throw new GeneralError(locale.USER_NOT_FOUND, 404);
    }
    return employee;
  }

  /**
   * @desc Get PLI ratings for an employee
   * @param {Object} employeeId Employee ID
   * @param {Object} locale Localization object
   * @return {Array} PLI ratings
   */
  async getPliRatingsForEmployee(employeeId, locale, month) {
    let pliRatings;
    if (month) {
      month = parseInt(month);
      pliRatings = await PLIRating.find({ menteeId: employeeId, month: month });
    } else {
      pliRatings = await PLIRating.find({ menteeId: employeeId });
    }
    if (!pliRatings || !pliRatings.length) {
      throw new GeneralError(locale.NO_PLI_RATINGS_FOUND, 404);
    }
    return pliRatings;
  }

  /**
   * @desc Process a single PLI rating
   * @param {Object} rating PLI rating object
   * @param {String} userDesignation User designation
   * @return {Object} Processed PLI rating
   */
  async processSinglePliRating(rating, userDesignation) {
    const pliRating = rating.toObject();

    // Store original IDs
    const originalMenteeId = pliRating.menteeId;
    const originalMentorId = pliRating.mentorId;

    // Add mentee details while preserving the original ID
    if (pliRating.menteeId) {
      const menteeData = await this.getUserDetails(pliRating.menteeId);
      if (menteeData) {
        pliRating.mentee = menteeData;
        // Keep the original menteeId
        pliRating.menteeId = originalMenteeId;
      }
    }

    // Add mentor details while preserving the original ID
    if (pliRating.mentorId) {
      const mentorData = await this.getUserDetails(pliRating.mentorId);
      if (mentorData) {
        pliRating.mentor = mentorData;
        // Keep the original mentorId
        pliRating.mentorId = originalMentorId;
      }
    }

    // Process project ratings
    if (pliRating.projectRatings) {
      pliRating.projectRatings = await this.processProjectRatings(
        pliRating.projectRatings,
        userDesignation
      );
    }

    return pliRating;
  }

  /**
   * @desc This function is used to get PLI ratings by employee ID
   * @param {Object} req Request
   * @param {Object} user User
   * @param {Object} locale Localization object
   * @return {Object} Response
   */
  async getPliRatingByEmployeeId(req, user, locale) {
    try {
      const { employeeId, month } = req.query;

      // Validate input
      this.validateGetPliRatingByEmployeeId(employeeId, "Employee ID", locale);

      // Find employee and get their PLI ratings
      const employee = await this.findEmployeeById(employeeId, locale);

      try {
        const pliRatings = await this.getPliRatingsForEmployee(
          employee._id,
          locale,
          month
        );
        const userDesignation = employee.designation || "Software Engineer";

        // Process each PLI rating
        const response = [];
        for (const rating of pliRatings) {
          const processedRating = await this.processSinglePliRating(
            rating,
            userDesignation
          );
          response.push(processedRating);
        }

        return { status: 1, data: response };
      } catch (innerError) {
        return { status: 0, message: innerError.message };
      }
    } catch (error) {
      return { status: 0, message: error.message };
    }
  }
}

module.exports = new GetPliRatingByEmployeeIdService();
