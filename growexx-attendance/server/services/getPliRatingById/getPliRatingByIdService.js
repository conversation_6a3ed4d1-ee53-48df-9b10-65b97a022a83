/**
 * Service to get PLI rating by ID with populated names
 */
const PLIRating = require("../../models/pliRating.model");
const User = require("../../models/user.model");
const Project = require("../../models/project.model");
const PLIParameters = require("../../models/pliParameters.model");
const GeneralError = require("../../util/GeneralError");
const ProjectSprintDataService = require("../getProjectSprintData/projectSprintDataService");

class GetPliRatingByIdService {
  /**
   * @desc This function gets the PLI parameter document
   * <AUTHOR>
   * @since 21/05/2025
   * @returns {Promise<Object>} PLI parameter document
   */
  static async getPliParameterDoc() {
    return await PLIParameters.findOne({});
  }

  /**
   * @desc Validate input parameters and return default value if invalid
   * @param {String} designation Employee designation
   * @param {String} projectType Project type
   * @param {String} parentParameter Parent parameter name
   * @param {String} childParameterId Child parameter ID/name
   * @returns {Boolean} True if all parameters are valid
   */
  static validateWeightageParams(
    designation,
    projectType,
    parentParameter,
    childParameterId
  ) {
    return designation && projectType && parentParameter && childParameterId;
  }

  /**
   * @desc Find the role document from PLI parameters
   * @param {Object} pliParameterDoc PLI parameter document
   * @param {String} designation Employee designation
   * @returns {Object|null} Role document if found, null otherwise
   */
  static findRoleDoc(pliParameterDoc, designation) {
    if (!pliParameterDoc || !pliParameterDoc.roleParameters) {
      return null;
    }

    // Map designation to role
    const role = ProjectSprintDataService.mapDesignationToRole(designation);

    // Find and return role parameters for this role
    const roleDoc = pliParameterDoc.roleParameters.find(
      (doc) => doc.applicableRole === role
    );
    return roleDoc && roleDoc.parameters ? roleDoc : null;
  }

  /**
   * @desc Find parameter document for project type and parent parameter
   * @param {Object} roleDoc Role document
   * @param {String} projectType Project type
   * @param {String} parentParameter Parent parameter name
   * @returns {Object|null} Parameter document if found, null otherwise
   */
  static findParameterDoc(roleDoc, projectType, parentParameter) {
    if (!roleDoc) {
      return null;
    }

    // Find parameter for this project type and parent parameter
    const parameterDoc = roleDoc.parameters.find(
      (parameter) =>
        parameter.parentParameter === parentParameter &&
        parameter.projectType === projectType
    );

    return parameterDoc && parameterDoc.childParameters ? parameterDoc : null;
  }

  /**
   * @desc This function gets the weightage for a child parameter
   * <AUTHOR>
   * @since 21/05/2025
   * @param {String} designation Employee designation
   * @param {String} projectType Project type (Fixed/Dedicated)
   * @param {String} parentParameter Parent parameter name
   * @param {String} childParameterId Child parameter ID/name
   * @returns {Number} Weightage of the child parameter
   */
  static async getChildParameterWeighatge(
    designation,
    projectType,
    parentParameter,
    childParameterId
  ) {
    try {
      // Validate input parameters
      if (
        !this.validateWeightageParams(
          designation,
          projectType,
          parentParameter,
          childParameterId
        )
      ) {
        return 0;
      }

      // Get PLI parameter document
      const pliParameterDoc = await this.getPliParameterDoc();

      // Find role document
      const roleDoc = this.findRoleDoc(pliParameterDoc, designation);
      if (!roleDoc) {
        return 0;
      }

      // Find parameter document
      const parameterDoc = this.findParameterDoc(
        roleDoc,
        projectType,
        parentParameter
      );
      if (!parameterDoc) {
        return 0;
      }

      // Find child parameter by name
      const childParameterDoc = parameterDoc.childParameters.find(
        (child) => child.name === childParameterId
      );
      if (!childParameterDoc) {
        return 0;
      }

      // Return the weightage, defaulting to 0 if undefined
      return childParameterDoc.weightage || 0;
    } catch (error) {
      return 0;
    }
  }

  /**
   * @desc Get user details and format them for response
   * @param {ObjectId} userId User ID
   * @returns {Object|null} Formatted user data or null if user not found
   */
  static async getUserDetails(userId) {
    try {
      const user = await User.findById(userId);
      if (!user) {
        return null;
      }

      const userName = `${user.firstName} ${user.lastName}`.trim();
      return {
        _id: userId, // Include the original ObjectId
        id: userId.toString(), // Include as string for frontend compatibility
        name: userName,
        email: user.email,
        employeeId: user.employeeId,
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * @desc Get project details and format them for response
   * @param {ObjectId} projectId Project ID
   * @returns {Object|null} Formatted project data or null if project not found
   */
  static async getProjectDetails(projectId) {
    try {
      const project = await Project.findById(projectId);
      if (!project) {
        return null;
      }

      // Special case handling for project names
      let projectName = project.projectName || project.name;

      // Map specific project names (e.g., "PSI II" to "COE")
      if (projectName === "PSI II") {
        projectName = "COE";
      }

      return {
        _id: projectId, // Include the original ObjectId
        id: projectId.toString(), // Include as string for frontend compatibility
        name: projectName,
        code: project.projectCode,
        client: project.clientName,
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * @desc Create parameter object from parameter score
   * @param {Object} parameterScore Parameter score object
   * @returns {Object} Parameter object with name and type
   */
  static createParameterObject(parameterScore) {
    return {
      name: parameterScore.parentParameter,
      type: parameterScore.projectType,
    };
  }

  /**
   * @desc Calculate average score from sprint scores
   * @param {Array} sprintScores Array of sprint scores
   * @returns {Number} Calculated average score
   */
  static calculateAverageScore(sprintScores) {
    if (!sprintScores || !sprintScores.length) {
      return 0;
    }

    const scores = sprintScores
      .map((sprint) => Number(sprint.score))
      .filter((score) => !isNaN(score));

    if (!scores.length) {
      return 0;
    }

    const totalScore = scores.reduce((sum, score) => sum + score, 0);
    return Number((totalScore / scores.length).toFixed(2));
  }

  /**
   * @desc Get user designation from response or user object
   * @param {Object} response Response object with mentee information
   * @returns {String} User designation or default value
   */
  static async getUserDesignation(response) {
    try {
      if (!response.menteeId) {
        return "Software Engineer";
      }

      const userDetails = await User.findById(response.menteeId).lean();
      return userDetails?.designation || "Software Engineer";
    } catch (error) {
      return "Software Engineer";
    }
  }

  /**
   * @desc Process child scores for a parameter
   * @param {Array} childScores Array of child scores
   * @param {Object} parameterInfo Parameter information
   * @param {String} userDesignation User designation
   * @returns {Array} Processed child scores
   */
  static async processChildScores(childScores, parameterInfo, userDesignation) {
    const processedScores = [];

    for (const childScore of childScores) {
      const processedScore = { ...childScore };

      // Store the original child parameter ID
      const originalChildParameterId = childScore.childParameterId;

      processedScore.childParameter = childScore.childParameterId;
      processedScore._id = originalChildParameterId; // Keep the original ID
      processedScore.childParameterId = originalChildParameterId; // Keep the original ID

      // 1. Get child parameter weightage
      processedScore.childParameterWeightage =
        await this.getChildParameterWeighatge(
          userDesignation,
          parameterInfo.type,
          parameterInfo.name,
          processedScore.childParameter
        );

      // 2. Calculate average of sprint scores
      processedScore.calculation = this.calculateAverageScore(
        childScore.sprintScores
      );

      // 3. Calculate weightage average
      processedScore.weightageAverage = Number(
        (
          processedScore.calculation * processedScore.childParameterWeightage
        ).toFixed(2)
      );

      processedScores.push(processedScore);
    }

    return processedScores;
  }

  /**
   * @desc Process parameter scores for a project rating
   * @param {Array} parameterScores Array of parameter scores
   * @param {String} userDesignation User designation
   * @returns {Array} Processed parameter scores
   */
  /**
   * @desc Process parameter scores for a PLI rating
   * @param {Array} parameterScores Array of parameter scores
   * @param {String} userDesignation User designation
   * @returns {Array} Processed parameter scores
   */
  static async processParameterScores(parameterScores, userDesignation) {
    const processedScores = [];

    for (const parameterScore of parameterScores) {
      const processedItem = await this.processSingleParameterScore(
        parameterScore,
        userDesignation
      );
      processedScores.push(processedItem);
    }

    return processedScores;
  }

  /**
   * @desc Process a single parameter score
   * @param {Object} parameterScore Parameter score to process
   * @param {String} userDesignation User designation
   * @return {Object} Processed parameter score
   */
  static async processSingleParameterScore(parameterScore, userDesignation) {
    const processedScore = { ...parameterScore };

    if (!processedScore.parameterId) {
      return processedScore;
    }

    try {
      // Store the original parameter ID
      const originalParameterId = processedScore.parameterId;

      // Create parameter object
      processedScore.parameter = this.createParameterObject(processedScore);

      // Add the original parameter ID to the parameter object
      processedScore.parameter._id = originalParameterId;
      processedScore.parameter.id = originalParameterId;

      // Process child scores if they exist
      await this.processChildScoresIfNeeded(processedScore, userDesignation);

      // Clean up but keep parameterId
      this.cleanupExtractedFields(processedScore);

      // Add back the parameter ID
      processedScore._id = originalParameterId;
      processedScore.parameterId = originalParameterId;
    } catch (error) {
      // Handle errors and still create parameter object
      processedScore.parameter = this.createParameterObject(processedScore);
      this.cleanupExtractedFields(processedScore);
    }

    return processedScore;
  }

  /**
   * @desc Clean up extracted fields but keep parameterId
   * @param {Object} processedScore Parameter score to process
   */
  static cleanupExtractedFields(processedScore) {
    // Don't delete parameterId anymore
    delete processedScore.projectType;
    delete processedScore.parentParameter;
  }

  /**
   * @desc Process child scores if they exist
   * @param {Object} processedScore Parameter score
   * @param {String} userDesignation User designation
   */
  static async processChildScoresIfNeeded(processedScore, userDesignation) {
    if (!processedScore.childScores || !processedScore.childScores.length) {
      return;
    }

    processedScore.childScores = await this.processChildScores(
      processedScore.childScores,
      processedScore.parameter,
      userDesignation
    );
  }

  /**
   * @desc Process project ratings for a PLI rating
   * @param {Array} projectRatings Array of project ratings
   * @param {String} userDesignation User designation
   * @returns {Array} Processed project ratings
   */
  static async processProjectRatings(projectRatings, userDesignation) {
    const processedRatings = [];

    for (const projectRating of projectRatings) {
      const processedRating = { ...projectRating };

      // Store the original project ID
      const originalProjectId = processedRating.projectId;

      // Replace project ID with project details
      if (processedRating.projectId) {
        const projectData = await this.getProjectDetails(
          processedRating.projectId
        );
        if (projectData) {
          processedRating.project = projectData;
          // Keep the original projectId
          processedRating.projectId = originalProjectId;
        }
      }

      // Process parameter scores
      if (
        processedRating.parameterScores &&
        processedRating.parameterScores.length
      ) {
        processedRating.parameterScores = await this.processParameterScores(
          processedRating.parameterScores,
          userDesignation
        );
      }

      processedRatings.push(processedRating);
    }

    return processedRatings;
  }

  /**
   * @desc This function is being used to get PLI rating by ID with populated names
   * <AUTHOR>
   * @since 16/05/2025
   * @param {Object} params - Query parameters
   * @param {string} params.pliRatingId - ID of the PLI rating
   * @param {Function} locale - Localization function
   * @returns {Object} PLI rating with populated names
   */
  static async getPliRatingById(params, locale) {
    const { pliRatingId } = params;

    // Find PLI rating by ID
    const pliRating = await PLIRating.findById(pliRatingId);
    if (!pliRating) {
      throw new GeneralError(locale("PLI_RATING_NOT_FOUND"), 404);
    }

    // Create a response object with names instead of IDs
    const response = pliRating.toObject();

    // Store original IDs
    const originalMenteeId = response.menteeId;
    const originalMentorId = response.mentorId;

    // Get mentee details and add to response
    if (response.menteeId) {
      const menteeData = await this.getUserDetails(response.menteeId);
      if (menteeData) {
        response.mentee = menteeData;
        // Keep the original menteeId
        response.menteeId = originalMenteeId;
      }
    }

    // Get mentor details and add to response
    if (response.mentorId) {
      const mentorData = await this.getUserDetails(response.mentorId);
      if (mentorData) {
        response.mentor = mentorData;
        // Keep the original mentorId
        response.mentorId = originalMentorId;
      }
    }

    // Get user designation for parameter processing
    const userDesignation = await this.getUserDesignation(response);

    // Process project ratings if they exist
    if (response.projectRatings && response.projectRatings.length) {
      response.projectRatings = await this.processProjectRatings(
        response.projectRatings,
        userDesignation
      );
    }

    return response;
  }
}

module.exports = GetPliRatingByIdService;
