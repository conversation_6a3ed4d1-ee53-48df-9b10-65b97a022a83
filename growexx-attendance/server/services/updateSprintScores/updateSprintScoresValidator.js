const validation = require("../../util/validation");
const GeneralError = require("../../util/GeneralError");
const mongoose = require("mongoose");

/**
 * Class represents validations for updating sprint scores in PLI Rating.
 */
class UpdateSprintScoresValidator extends validation {
  constructor(body, locale) {
    super(locale);
    this.body = body;
  }

  /**
   * @desc This function is being used to validate update sprint scores request
   * <AUTHOR>
   * @since 13/05/2025
   */
  async validateUpdateSprintScores() {
    await this.validateMenteeId();
    await this.validateMentorId();
    await this.validateMonth();
    await this.validateYear();
    await this.validateProjectRatings();
  }

  /**
   * @desc This function is being used to validate mentee ID
   * <AUTHOR>
   * @since 13/05/2025
   */
  async validateMenteeId() {
    if (!this.body.menteeId) {
      throw new GeneralError(this.__(this.REQUIRED, "Mentee ID"), 400);
    }
  }

  /**
   * @desc This function is being used to validate mentor ID
   * <AUTHOR>
   * @since 14/05/2025
   */
  async validateMentorId() {
    // If it's a Super Admin override, mentor ID is optional
    if (this.body.superAdminOverride === true) {
      console.log(
        "Super Admin override detected, skipping mentorId validation"
      );
      return;
    }

    if (!this.body.mentorId) {
      throw new GeneralError(this.__(this.REQUIRED, "Mentor ID"), 400);
    }

    if (!mongoose.Types.ObjectId.isValid(this.body.mentorId)) {
      throw new GeneralError(this.__(this.NOT_VALID, "Mentor ID"), 400);
    }
  }

  /**
   * @desc This function is being used to validate month
   * <AUTHOR>
   * @since 13/05/2025
   */
  async validateMonth() {
    if (!this.body.month) {
      throw new GeneralError(this.__(this.REQUIRED, "Month"), 400);
    }
    const month = Number(this.body.month);
    if (isNaN(month) || month < 1 || month > 12) {
      throw new GeneralError(this.__(this.NOT_VALID, "Month"), 400);
    }
  }

  /**
   * @desc This function is being used to validate year
   * <AUTHOR>
   * @since 13/05/2025
   */
  async validateYear() {
    if (!this.body.year) {
      throw new GeneralError(this.__(this.REQUIRED, "Year"), 400);
    }
    const year = Number(this.body.year);
    if (isNaN(year) || year < 2000 || year > 2100) {
      throw new GeneralError(this.__(this.NOT_VALID, "Year"), 400);
    }
  }

  /**
   * @desc This function is being used to validate project ratings
   * <AUTHOR>
   * @since 13/05/2025
   */
  async validateProjectRatings() {
    if (!this.body.projectRatings || !Array.isArray(this.body.projectRatings)) {
      throw new GeneralError(this.__(this.NOT_VALID, "Project ratings"), 400);
    }

    if (this.body.projectRatings.length === 0) {
      throw new GeneralError(
        this.__(this.REQUIRED, "At least one project rating"),
        400
      );
    }

    for (let i = 0; i < this.body.projectRatings.length; i++) {
      const projectRating = this.body.projectRatings[i];

      // Validate project ID
      if (!projectRating.projectId) {
        throw new GeneralError(
          this.__(this.REQUIRED, `Project ID in project rating ${i + 1}`),
          400
        );
      }

      // Validate parameter scores
      if (
        !projectRating.parameterScores ||
        !Array.isArray(projectRating.parameterScores)
      ) {
        throw new GeneralError(
          this.__(
            this.NOT_VALID,
            `Parameter scores in project rating ${i + 1}`
          ),
          400
        );
      }

      for (let j = 0; j < projectRating.parameterScores.length; j++) {
        const parameterScore = projectRating.parameterScores[j];

        // Validate parameter ID
        if (!parameterScore.parameterId) {
          throw new GeneralError(
            this.__(
              this.REQUIRED,
              `Parameter ID in parameter score ${j + 1} of project rating ${
                i + 1
              }`
            ),
            400
          );
        }

        // Validate child scores
        if (
          !parameterScore.childScores ||
          !Array.isArray(parameterScore.childScores)
        ) {
          throw new GeneralError(
            this.__(
              this.NOT_VALID,
              `Child scores in parameter score ${j + 1} of project rating ${
                i + 1
              }`
            ),
            400
          );
        }

        for (let k = 0; k < parameterScore.childScores.length; k++) {
          const childScore = parameterScore.childScores[k];

          // Validate child parameter ID
          if (!childScore.childParameterId) {
            throw new GeneralError(
              this.__(
                this.REQUIRED,
                `Child parameter ID in child score ${k + 1} of parameter score ${
                  j + 1
                } of project rating ${i + 1}`
              ),
              400
            );
          }

          // Validate sprint scores
          if (
            !childScore.sprintScores ||
            !Array.isArray(childScore.sprintScores)
          ) {
            throw new GeneralError(
              this.__(
                this.NOT_VALID,
                `Sprint scores in child score ${k + 1} of parameter score ${
                  j + 1
                } of project rating ${i + 1}`
              ),
              400
            );
          }

          for (let l = 0; l < childScore.sprintScores.length; l++) {
            const sprintScore = childScore.sprintScores[l];

            // Validate sprint number
            if (!sprintScore.sprintNumber) {
              throw new GeneralError(
                this.__(
                  this.REQUIRED,
                  `Sprint number in sprint score ${l + 1} of child score ${
                    k + 1
                  } of parameter score ${j + 1} of project rating ${i + 1}`
                ),
                400
              );
            }

            // Validate that sprint number is a string
            if (typeof sprintScore.sprintNumber !== "string") {
              throw new GeneralError(
                this.__(
                  this.INVALID,
                  `Sprint number in sprint score ${l + 1} of child score ${
                    k + 1
                  } of parameter score ${j + 1} of project rating ${i + 1} must be a string`
                ),
                400
              );
            }

            // Validate score
            if (sprintScore.score === undefined || sprintScore.score === null) {
              throw new GeneralError(
                this.__(
                  this.REQUIRED,
                  `Score in sprint score ${l + 1} of child score ${
                    k + 1
                  } of parameter score ${j + 1} of project rating ${i + 1}`
                ),
                400
              );
            }

            const score = Number(sprintScore.score);
            if (isNaN(score)) {
              throw new GeneralError(
                this.__(
                  this.NOT_VALID,
                  `Score in sprint score ${l + 1} of child score ${
                    k + 1
                  } of parameter score ${j + 1} of project rating ${i + 1} must be a valid number`
                ),
                400
              );
            }
          }
        }
      }
    }
  }
}

module.exports = UpdateSprintScoresValidator;
