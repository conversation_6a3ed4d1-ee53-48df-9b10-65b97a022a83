const UpdateSprintScoresService = require("./updateSprintScoresService");
const Utils = require("../../util/utilFunctions");

/**
 * Class represents controller for updating sprint scores in PLI Rating
 */
class UpdateSprintScoresController {
  /**
   * @desc This function is being used to update sprint scores in PLI rating
   * <AUTHOR>
   * @since 13/05/2025
   * @param {Object} req Request
   * @param {Object} req.body RequestBody
   * @param {function} res Response
   */
  static async updateSprintScores(req, res) {
    try {
      console.log("Request body:", JSON.stringify(req.body));

      // Get user from res.locals (set by auth middleware) instead of req.user
      const user = res.locals.user;
      console.log(
        "User from res.locals:",
        user ? `ID: ${user._id}` : "undefined"
      );

      // Check for Super Admin override flag in headers or body
      const headerOverride = req.headers["x-super-admin-override"] === "true";
      const bodyOverride = req.body.superAdminOverride === true;

      // Ensure the superAdminOverride flag is properly set in the request body
      req.body.superAdminOverride = headerOverride || bodyOverride;

      if (req.body.superAdminOverride) {
        console.log("SUPER ADMIN OVERRIDE MODE DETECTED");

        // For Super Admin override, ensure user information is available
        if (!user) {
          console.error(
            "Error: user is undefined but superAdminOverride is true"
          );
          return Utils.sendResponse(
            { message: "Super Admin user information not found" },
            null,
            res,
            "Super Admin user information not found"
          );
        }

        console.log("Super Admin user ID:", user._id);

        // For Super Admin override, if mentorId is not provided, use the user's ID
        if (!req.body.mentorId) {
          console.log("Setting mentorId to Super Admin ID:", user._id);
          req.body.mentorId = user._id;
        }

        // Set the user in the request object for the service to use
        req.user = user;
      }

      const data = await UpdateSprintScoresService.updateSprintScores(
        req,
        res.__
      );
      Utils.sendResponse(null, data, res, res.__("SUCCESS"));
    } catch (error) {
      console.error("Error in updateSprintScores:", error);
      console.error("Error stack:", error.stack);
      Utils.sendResponse(error, null, res, error.message);
    }
  }
}

module.exports = UpdateSprintScoresController;
