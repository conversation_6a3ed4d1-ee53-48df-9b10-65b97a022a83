const PLIRating = require('../../models/pliRating.model');
const UpdateSprintScoresValidator = require('./updateSprintScoresValidator');
const GeneralError = require('../../util/GeneralError');
const User = require('../../models/user.model');
const Project = require('../../models/project.model');
const sendPliNotificationEmail = require('../notificationMentee/sendPLIRatingNotification');
const sendPliOverrideNotification = require('../notificationMentee/sendPliOverrideNotification');
const PLIParameters = require('../../models/pliParameters.model');
const mongoose = require('mongoose');

/**
 * Class represents services for updating sprint scores in PLI Rating
 */
class UpdateSprintScoresService {
    /**
   * @desc This function is being used to update sprint scores in PLI rating
   * <AUTHOR>
   * @since 13/05/2025
   * @param {Object} req Request
   * @param {function} locale Translation function
   * @return {Object} Updated PLI rating
   */
    static async updateSprintScores (req, locale) {
        try {
            console.log('Starting updateSprintScores service');
            const Validator = new UpdateSprintScoresValidator(req.body, locale);
            await Validator.validateUpdateSprintScores();

            const {
                menteeId,
                mentorId,
                month,
                year,
                projectRatings,
                superAdminOverride
            } = req.body;

            // Process project ratings for Super Admin override
            if (superAdminOverride) {
                await this.processProjectAndParameterIds(projectRatings);
            }

            // Check if PLI rating exists for the given mentee, month, and year
            let pliRating = await PLIRating.findOne({
                menteeId,
                month,
                year
            });

            // Check if this PLI is already frozen by a Super Admin
            if (pliRating && pliRating.isFrozen && pliRating.superAdminOverride) {
                throw new GeneralError(
                    locale('PLI_ALREADY_FROZEN_BY_SUPERADMIN') ||
            'This PLI has been finalized by a Super Admin and cannot be modified further',
                    400
                );
            }

            // Check if this PLI is already frozen and the current user is not a Super Admin
            if (pliRating && pliRating.isFrozen && !superAdminOverride) {
                throw new GeneralError(
                    locale('PLI_ALREADY_FROZEN') ||
            'This PLI rating is already frozen and cannot be edited',
                    400
                );
            }

            // If PLI rating doesn't exist, create a new one
            if (!pliRating) {
                // Create a new PLI rating with the provided project ratings
                if (!mentorId) {
                    throw new GeneralError(locale('MENTOR_ID_REQUIRED'));
                }
                pliRating = new PLIRating({
                    menteeId,
                    mentorId,
                    month,
                    year,
                    status: 'Submitted',
                    projectRatings: projectRatings,
                    createdAt: Date.now(),
                    updatedAt: Date.now()
                });
            } else {
                // If PLI rating exists, completely replace the project ratings
                pliRating.projectRatings = projectRatings;
                pliRating.updatedAt = Date.now();
            }

            // Update the PLI rating
            pliRating.updatedAt = Date.now();

            // Save the updated PLI rating
            await pliRating.save();

            // Ensure _id is included in the response
            const response = pliRating.toObject ? pliRating.toObject() : pliRating;
            if (!response._id && pliRating._id) {
                response._id = pliRating._id;
            }

            // Update the PLI rating
            pliRating.updatedAt = Date.now();

            try {
                // Save the updated PLI rating
                await pliRating.save();
                // Get mentor details
                const mentor = await User.findById(mentorId);
                const mentorName = mentor
                    ? `${mentor.firstName} ${mentor.lastName}`
                    : 'Your Mentor';

                // Get project name (using the first project for simplicity)
                const projectId = projectRatings[0]?.projectId;
                let projectName = 'Your Project';
                if (projectRatings && projectRatings.length > 0) {
                    const projectInfo = await Project.findById(projectId);
                    if (projectInfo) {
                        projectName = projectInfo.projectName || projectInfo.name;
                    }

                }


                // Get month name
                // const monthNames = [
                //     'January', 'February', 'March', 'April', 'May', 'June',
                //     'July', 'August', 'September', 'October', 'November', 'December'
                // ];
                // const monthName = monthNames[month - 1] || 'Current Month';

                // Use the final score from the saved PLI rating
                const score = pliRating.finalScore || 'N/A';

                // Prepare payload for email
                const emailPayload = {
                    month: month,
                    year,
                    score,
                    mentorName,
                    projectName,
                    companyName: 'Growexx',
                    pliRatingId: response._id.toString()
                };
                // Send notification email
                if (superAdminOverride) {
                    await sendPliOverrideNotification(menteeId, mentorId, emailPayload);
                }
                else {
                    await sendPliNotificationEmail(menteeId, emailPayload);
                }
            } catch (emailError) {
                // Don't fail the entire request if email fails
                console.error('Failed to send email notification:', emailError);
            }

            return response;
        } catch (error) {
            console.error('Error in updateSprintScores service:', error);
            throw error;
        }
    }

    /**
   * @desc Process project and parameter IDs for Super Admin override
   * @param {Array} projectRatings Project ratings array
   * @returns {Promise<void>}
   */
    static async processProjectAndParameterIds (projectRatings) {
        if (!projectRatings || !Array.isArray(projectRatings)) {
            return;
        }



        // Process each project rating
        for (let i = 0; i < projectRatings.length; i++) {
            const projectRating = projectRatings[i];

            try {
                // Handle projectId - if it's a string, find or create a Project
                if (
                    typeof projectRating.projectId === 'string' &&
          !mongoose.Types.ObjectId.isValid(projectRating.projectId)
                ) {
                    console.log(
                        `Project ID "${projectRating.projectId}" is not a valid ObjectId, looking up or creating project`
                    );

                    // Try to find an existing project with this name
                    let project = await Project.findOne({
                        name: projectRating.projectId
                    });

                    // If project doesn't exist, create a new one
                    if (!project) {
                        console.log(
                            `Creating new project with name: ${projectRating.projectId}`
                        );
                        project = new Project({
                            name: projectRating.projectId,
                            isActive: true,
                            createdAt: Date.now(),
                            updatedAt: Date.now()
                        });

                        await project.save();
                        console.log(`Created new project with ID: ${project._id}`);
                    } else {
                        console.log(`Found existing project with ID: ${project._id}`);
                    }

                    // Replace the string projectId with the ObjectId
                    projectRating.projectId = project._id.toString();
                }

                // Process parameter scores
                if (
                    !projectRating.parameterScores ||
          !Array.isArray(projectRating.parameterScores)
                ) {
                    continue;
                }

                // Process each parameter score
                for (let j = 0; j < projectRating.parameterScores.length; j++) {
                    const parameterScore = projectRating.parameterScores[j];

                    try {
                        // Check if this is a temporary parameter ID or invalid ObjectId
                        if (
                            !parameterScore.parameterId ||
              (typeof parameterScore.parameterId === 'string' &&
                (!mongoose.Types.ObjectId.isValid(parameterScore.parameterId) ||
                  parameterScore.parameterId.startsWith('temp_param_')))
                        ) {
                            console.log(
                                `Processing parameter ID: ${parameterScore.parameterId}`
                            );

                            // Try to find an existing parameter for this project type
                            const projectType = parameterScore.projectType || 'Fixed';
                            const parentParameter =
                parameterScore.parentParameter || 'Project';

                            console.log(
                                `Looking for parameter with projectType=${projectType}, parentParameter=${parentParameter}`
                            );

                            let pliParameter = await PLIParameters.findOne({
                                projectType,
                                parentParameter
                            });

                            // If parameter doesn't exist, create a new one
                            if (!pliParameter) {
                                console.log('Creating new PLI parameter');
                                pliParameter = new PLIParameters({
                                    projectType,
                                    parentParameter,
                                    isActive: true,
                                    createdAt: Date.now(),
                                    updatedAt: Date.now()
                                });

                                await pliParameter.save();
                                console.log(
                                    'Created new PLI parameter with ID:',
                                    pliParameter._id
                                );
                            } else {
                                console.log(
                                    'Found existing PLI parameter with ID:',
                                    pliParameter._id
                                );
                            }

                            // Replace the temporary ID with the real one
                            parameterScore.parameterId = pliParameter._id.toString();
                        }
                    } catch (error) {
                        console.error(
                            `Error processing parameter ID at index ${j}:`,
                            error
                        );
                        // Continue processing other parameters even if one fails
                    }
                }
            } catch (error) {
                console.error(`Error processing project rating at index ${i}:`, error);
                // Continue processing other project ratings even if one fails
            }
        }

        console.log('Finished processing project and parameter IDs');
    }
}

module.exports = UpdateSprintScoresService;
