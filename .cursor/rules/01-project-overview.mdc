---
description: 
globs: 
alwaysApply: true
---
# Timesheet Project Overview
This is a timesheet application with both frontend and backend components:
- Frontend: @growexx-attendance-fe: React application
- Backend: @growexx-attendance: Node.js backend
## Project Structure
### Frontend (React)
- @app/: Main application code
  - @components/: Reusable UI components
  - @containers/: Page containers with business logic
  - @utils/: Utility functions
### Backend (Node.js)
- @server/: Backend server code
  - @models/: Database models
  - @routes/: API routes
  - @services/: Business logic services
## Development Guidelines
- Follow existing code patterns when adding new features
- Frontend uses Redux for state management
- Tests are written using Je<PERSON> for frontend and <PERSON><PERSON>/Chai for backend